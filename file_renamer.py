# -*- coding: utf-8 -*-
"""
文件和文件夹重命名功能模块
兼容Python 3.6及以上版本
"""

import os
import logging
from pinyin_converter import (
    convert_filename_to_pinyin,
    convert_folder_name_to_pinyin,
    has_chinese_characters
)


class FileRenamer:
    """文件和文件夹重命名器"""
    
    def __init__(self):
        """初始化重命名器"""
        self.stats = {
            'folders_renamed': 0,
            'files_renamed': 0,
            'errors': 0
        }
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def get_unique_name(self, directory, new_name):
        """
        获取唯一的文件/文件夹名称，处理重名冲突
        
        Args:
            directory (str): 目录路径
            new_name (str): 新名称
            
        Returns:
            str: 唯一的名称
        """
        if not os.path.exists(os.path.join(directory, new_name)):
            return new_name
        
        # 处理重名冲突
        base_name = new_name
        extension = ""
        
        # 如果是文件，分离扩展名
        if '.' in new_name:
            base_name, extension = new_name.rsplit('.', 1)
            extension = '.' + extension
        
        counter = 1
        while True:
            unique_name = f"{base_name}_{counter}{extension}"
            if not os.path.exists(os.path.join(directory, unique_name)):
                return unique_name
            counter += 1
    
    def rename_file(self, file_path):
        """
        重命名单个文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            tuple: (是否成功, 新文件路径)
        """
        try:
            directory = os.path.dirname(file_path)
            old_filename = os.path.basename(file_path)
            
            # 检查是否包含中文字符
            if not has_chinese_characters(old_filename):
                return True, file_path
            
            # 转换文件名
            new_filename = convert_filename_to_pinyin(old_filename)
            
            # 处理重名冲突
            unique_filename = self.get_unique_name(directory, new_filename)
            new_file_path = os.path.join(directory, unique_filename)
            
            # 执行重命名
            os.rename(file_path, new_file_path)
            
            self.logger.info(f"文件重命名: {old_filename} -> {unique_filename}")
            self.stats['files_renamed'] += 1
            
            return True, new_file_path
            
        except Exception as e:
            self.logger.error(f"重命名文件失败 {file_path}: {str(e)}")
            self.stats['errors'] += 1
            return False, file_path
    
    def rename_folder(self, folder_path):
        """
        重命名单个文件夹
        
        Args:
            folder_path (str): 文件夹路径
            
        Returns:
            tuple: (是否成功, 新文件夹路径)
        """
        try:
            parent_directory = os.path.dirname(folder_path)
            old_folder_name = os.path.basename(folder_path)
            
            # 检查是否包含中文字符
            if not has_chinese_characters(old_folder_name):
                return True, folder_path
            
            # 转换文件夹名
            new_folder_name = convert_folder_name_to_pinyin(old_folder_name)
            
            # 处理重名冲突
            unique_folder_name = self.get_unique_name(parent_directory, new_folder_name)
            new_folder_path = os.path.join(parent_directory, unique_folder_name)
            
            # 执行重命名
            os.rename(folder_path, new_folder_path)
            
            self.logger.info(f"文件夹重命名: {old_folder_name} -> {unique_folder_name}")
            self.stats['folders_renamed'] += 1
            
            return True, new_folder_path
            
        except Exception as e:
            self.logger.error(f"重命名文件夹失败 {folder_path}: {str(e)}")
            self.stats['errors'] += 1
            return False, folder_path
    
    def process_directory(self, root_path):
        """
        递归处理目录中的所有文件和文件夹
        
        Args:
            root_path (str): 根目录路径
            
        Returns:
            dict: 处理统计信息
        """
        if not os.path.exists(root_path):
            self.logger.error(f"目录不存在: {root_path}")
            return self.stats
        
        if not os.path.isdir(root_path):
            self.logger.error(f"路径不是目录: {root_path}")
            return self.stats
        
        self.logger.info(f"开始处理目录: {root_path}")
        
        # 使用os.walk递归遍历目录
        # 注意：需要从最深层开始处理，避免重命名父目录后路径失效
        all_dirs = []
        all_files = []
        
        for root, dirs, files in os.walk(root_path):
            # 收集所有目录（按深度排序，深的在前）
            for dir_name in dirs:
                dir_path = os.path.join(root, dir_name)
                all_dirs.append(dir_path)
            
            # 收集所有文件
            for file_name in files:
                file_path = os.path.join(root, file_name)
                all_files.append(file_path)
        
        # 按路径深度排序目录（深度大的在前，确保先处理子目录）
        all_dirs.sort(key=lambda x: x.count(os.sep), reverse=True)
        
        # 先处理所有文件
        self.logger.info(f"开始处理 {len(all_files)} 个文件...")
        for file_path in all_files:
            self.rename_file(file_path)
        
        # 再处理所有目录
        self.logger.info(f"开始处理 {len(all_dirs)} 个文件夹...")
        for dir_path in all_dirs:
            # 检查目录是否仍然存在（可能在之前的重命名中已被处理）
            if os.path.exists(dir_path):
                self.rename_folder(dir_path)
        
        self.logger.info("目录处理完成")
        return self.stats
    
    def get_statistics(self):
        """
        获取处理统计信息
        
        Returns:
            dict: 统计信息
        """
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'folders_renamed': 0,
            'files_renamed': 0,
            'errors': 0
        }
