中文转拼音批量重命名工具 - Windows 7简化版

本版本专门为解决Windows 7兼容性问题而创建。

如果遇到DLL缺失错误，请尝试以下解决方案：

方案1: 安装Microsoft Visual C++ Redistributable
1. 下载并安装 Microsoft Visual C++ 2015-2019 Redistributable (x64)
   下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe
2. 重启计算机
3. 重新运行程序

方案2: 以管理员权限运行
1. 右键点击exe文件
2. 选择"以管理员身份运行"

方案3: 兼容性模式
1. 右键点击exe文件
2. 选择"属性"
3. 切换到"兼容性"选项卡
4. 勾选"以兼容模式运行这个程序"
5. 选择"Windows 8"或"Windows 10"

使用方法：
1. 命令行模式：ChineseToPinyinRenamer.exe "目标文件夹路径"
2. 交互式模式：ChineseToPinyinRenamer.exe --interactive
3. 查看帮助：ChineseToPinyinRenamer.exe --help

注意事项：
• 请在使用前备份重要文件
• 程序会直接修改文件和文件夹名称，操作不可逆
• 建议先在测试目录中验证效果

如果问题仍然存在，可能需要：
• 升级到Windows 10或更新版本
• 使用Python源码版本运行程序
