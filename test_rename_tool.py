#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文转拼音批量重命名工具测试用例
兼容Python 3.6及以上版本
"""

import os
import sys
import tempfile
import shutil
import unittest
from unittest.mock import patch

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pinyin_converter import PinyinConverter
from file_renamer import FileRenamer
from txt_processor import TxtProcessor
from statistics_manager import StatisticsManager


class TestPinyinConverter(unittest.TestCase):
    """测试拼音转换器"""
    
    def setUp(self):
        """设置测试环境"""
        self.converter = PinyinConverter()
    
    def test_convert_chinese_to_pinyin(self):
        """测试中文转拼音功能"""
        test_cases = [
            ("你好世界", "ni hao shi jie"),
            ("测试文件", "ce shi wen jian"),
            ("Hello世界", "Hello shi jie"),
            ("", ""),
            ("123", "123"),
            ("中文，测试！", "zhong wen , ce shi !"),
        ]
        
        for chinese, expected in test_cases:
            with self.subTest(chinese=chinese):
                result = self.converter.convert_chinese_to_pinyin(chinese)
                self.assertEqual(result, expected)
    
    def test_convert_punctuation(self):
        """测试标点符号转换"""
        test_cases = [
            ("你好，世界！", "你好,世界!"),
            ("测试：成功？", "测试:成功?"),
            ("《书名》", "<书名>"),
            ("", ""),
        ]
        
        for chinese, expected in test_cases:
            with self.subTest(chinese=chinese):
                result = self.converter.convert_punctuation(chinese)
                self.assertEqual(result, expected)
    
    def test_convert_filename(self):
        """测试文件名转换"""
        test_cases = [
            ("测试文件.txt", "ce_shi_wen_jian.txt"),
            ("中文文档.docx", "zhong_wen_wen_dang.docx"),
            ("English.txt", "English.txt"),
            ("混合Chinese.pdf", "hun_he_Chinese.pdf"),
        ]
        
        for filename, expected in test_cases:
            with self.subTest(filename=filename):
                result = self.converter.convert_filename(filename)
                self.assertEqual(result, expected)
    
    def test_is_chinese_present(self):
        """测试中文字符检测"""
        test_cases = [
            ("中文", True),
            ("English", False),
            ("混合English", True),
            ("123", False),
            ("", False),
        ]
        
        for text, expected in test_cases:
            with self.subTest(text=text):
                result = self.converter.is_chinese_present(text)
                self.assertEqual(result, expected)


class TestFileRenamer(unittest.TestCase):
    """测试文件重命名器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.renamer = FileRenamer()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_get_unique_name(self):
        """测试获取唯一名称"""
        # 创建测试文件
        test_file = os.path.join(self.test_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        # 测试重名处理
        unique_name = self.renamer.get_unique_name(self.test_dir, "test.txt")
        self.assertEqual(unique_name, "test_1.txt")
        
        # 测试不重名的情况
        unique_name = self.renamer.get_unique_name(self.test_dir, "new_file.txt")
        self.assertEqual(unique_name, "new_file.txt")


class TestTxtProcessor(unittest.TestCase):
    """测试txt文件处理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.processor = TxtProcessor()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)
    
    def test_detect_encoding(self):
        """测试编码检测"""
        # 创建UTF-8编码的测试文件
        test_file = os.path.join(self.test_dir, "test_utf8.txt")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试中文内容")
        
        encoding = self.processor.detect_encoding(test_file)
        self.assertIn(encoding.lower(), ['utf-8', 'utf8'])
    
    def test_read_file_content(self):
        """测试文件内容读取"""
        # 创建测试文件
        test_file = os.path.join(self.test_dir, "test.txt")
        test_content = "测试中文内容，包含标点符号！"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        success, content, encoding = self.processor.read_file_content(test_file)
        self.assertTrue(success)
        self.assertEqual(content, test_content)


class TestStatisticsManager(unittest.TestCase):
    """测试统计管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.stats_manager = StatisticsManager()
    
    def test_merge_stats(self):
        """测试统计信息合并"""
        file_stats = {
            'folders_renamed': 5,
            'files_renamed': 10,
            'errors': 1
        }
        
        txt_stats = {
            'txt_files_processed': 3,
            'txt_files_skipped': 2,
            'errors': 0
        }
        
        self.stats_manager.merge_file_renamer_stats(file_stats)
        self.stats_manager.merge_txt_processor_stats(txt_stats)
        
        summary = self.stats_manager.get_summary()
        
        self.assertEqual(summary['folders_renamed'], 5)
        self.assertEqual(summary['files_renamed'], 10)
        self.assertEqual(summary['txt_files_processed'], 3)
        self.assertEqual(summary['txt_files_skipped'], 2)
        self.assertEqual(summary['total_errors'], 1)
    
    def test_calculate_success_rate(self):
        """测试成功率计算"""
        # 模拟一些统计数据
        self.stats_manager.total_stats = {
            'folders_renamed': 5,
            'files_renamed': 10,
            'txt_files_processed': 3,
            'txt_files_skipped': 0,
            'total_errors': 2,
            'processing_time': 0
        }
        
        success_rate = self.stats_manager.calculate_success_rate()
        # 总操作数: 5 + 10 + 3 + 2 = 20
        # 成功操作数: 20 - 2 = 18
        # 成功率: 18/20 * 100 = 90%
        self.assertEqual(success_rate, 90.0)


def create_test_directory_structure():
    """创建测试目录结构"""
    test_root = tempfile.mkdtemp(prefix="rename_tool_test_")
    
    # 创建测试目录结构
    dirs_to_create = [
        "中文文件夹",
        "English_Folder",
        "混合Folder",
        "中文文件夹/子文件夹",
    ]
    
    for dir_name in dirs_to_create:
        dir_path = os.path.join(test_root, dir_name)
        os.makedirs(dir_path, exist_ok=True)
    
    # 创建测试文件
    files_to_create = [
        ("测试文件.txt", "这是一个测试文件，包含中文内容！"),
        ("English_File.txt", "This is an English file."),
        ("混合File.txt", "这是混合内容的文件，包含English和中文。"),
        ("中文文件夹/子文件.txt", "子目录中的中文文件内容。"),
    ]
    
    for file_name, content in files_to_create:
        file_path = os.path.join(test_root, file_name)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    return test_root


def run_integration_test():
    """运行集成测试"""
    print("运行集成测试...")
    
    # 创建测试目录
    test_dir = create_test_directory_structure()
    print(f"测试目录创建于: {test_dir}")
    
    try:
        # 导入主程序
        from rename_tool import ChineseToPinyinRenamer
        
        # 创建重命名工具实例
        renamer = ChineseToPinyinRenamer()
        
        # 处理测试目录
        success = renamer.process_directory(test_dir, generate_report=True)
        
        if success:
            print("集成测试成功！")
            print(f"请检查测试目录: {test_dir}")
            print("注意：测试完成后请手动删除测试目录")
        else:
            print("集成测试失败！")
        
        return success
        
    except Exception as e:
        print(f"集成测试出错: {str(e)}")
        return False


if __name__ == '__main__':
    print("中文转拼音批量重命名工具 - 测试套件")
    print("="*50)
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # 运行集成测试
    integration_success = run_integration_test()
    
    print("\n" + "="*50)
    print("测试完成！")
    
    if integration_success:
        print("所有测试通过！")
    else:
        print("部分测试失败，请检查错误信息。")
