# 中文转拼音批量重命名工具 - 构建说明

本文档说明如何将Python项目打包成可在Windows 7上独立运行的exe文件。

## 项目结构

```
pinyin2/
├── 源代码文件/
│   ├── rename_tool.py          # 主程序入口
│   ├── pinyin_converter.py     # 中文转拼音核心功能
│   ├── file_renamer.py         # 文件重命名功能
│   ├── txt_processor.py        # txt文件处理功能
│   └── statistics_manager.py   # 统计管理功能
├── 构建脚本/
│   ├── build_exe.py            # 基础构建脚本
│   ├── build_exe_advanced.py   # 高级构建脚本
│   └── create_distribution.py  # 分发包创建脚本
├── 配置文件/
│   ├── requirements.txt        # Python依赖
│   └── version_info.txt        # 版本信息（自动生成）
└── 输出目录/
    ├── dist/                   # exe文件输出目录
    ├── release/                # 基础发布包
    ├── release_advanced/       # 高级发布包
    └── ChineseToPinyinRenamer_v1.0.0/ # 最终分发包
```

## 构建步骤

### 1. 环境准备

确保已安装Python 3.6及以上版本，然后安装依赖：

```bash
pip install -r requirements.txt
```

### 2. 基础构建

运行基础构建脚本：

```bash
python build_exe.py
```

这将创建：
- `dist/ChineseToPinyinRenamer.exe` - 基础exe文件
- `release/` - 基础发布包

### 3. 高级构建（推荐）

运行高级构建脚本：

```bash
python build_exe_advanced.py
```

这将创建：
- 包含版本信息的exe文件
- 完整的用户文档
- 批处理启动文件
- 快速测试脚本

### 4. 创建最终分发包

运行分发包创建脚本：

```bash
python create_distribution.py
```

这将创建：
- `ChineseToPinyinRenamer_v1.0.0/` - 完整分发目录
- `ChineseToPinyinRenamer_v1.0.0.zip` - 压缩分发包

## 构建输出

### exe文件特性
- **文件大小**: 约8.9MB
- **兼容性**: Windows 7/8/10/11 (64位)
- **依赖**: 无需安装Python或其他依赖
- **启动时间**: 约2-3秒

### 分发包内容
- `ChineseToPinyinRenamer.exe` - 主程序
- `启动工具.bat` - 用户友好的启动界面
- `快速测试.bat` - 自动创建测试环境
- `使用说明.txt` - 详细使用说明
- `系统要求.txt` - 系统兼容性说明
- `许可证.txt` - 软件许可证
- `更新日志.txt` - 版本更新记录
- `卸载说明.txt` - 卸载指南

## Windows 7 兼容性

### 技术要求
- Windows 7 SP1 及以上版本
- 64位操作系统
- 至少512MB可用内存
- 50MB硬盘空间

### 兼容性保证
1. **Python版本**: 使用Python 3.6兼容的代码
2. **依赖库**: 选择兼容Windows 7的库版本
3. **系统API**: 避免使用Windows 10专有API
4. **文件路径**: 处理长路径和Unicode字符

### 测试建议
在以下环境中测试exe文件：
- Windows 7 SP1 (64位)
- Windows 8.1 (64位)
- Windows 10 (64位)
- Windows 11 (64位)

## 构建优化

### 减小文件大小
1. 使用`--exclude-module`排除不需要的模块
2. 启用UPX压缩（如果可用）
3. 移除调试信息

### 提高启动速度
1. 使用`--onefile`创建单文件exe
2. 优化导入语句
3. 延迟加载非关键模块

### 增强安全性
1. 添加数字签名（需要代码签名证书）
2. 包含版本信息
3. 添加图标文件

## 故障排除

### 常见问题

**问题1**: 构建失败，提示缺少模块
```
解决方案: 检查requirements.txt，确保所有依赖已安装
pip install -r requirements.txt
```

**问题2**: exe文件无法在Windows 7上运行
```
解决方案: 确保使用Python 3.6-3.8构建，避免使用过新的Python版本
```

**问题3**: 文件大小过大
```
解决方案: 使用--exclude-module排除不需要的模块
或考虑使用--onedir模式
```

**问题4**: 启动速度慢
```
解决方案: 优化导入语句，使用延迟导入
或考虑使用--onedir模式而非--onefile
```

### 调试方法

1. **查看构建日志**: 检查PyInstaller的输出信息
2. **测试依赖**: 在目标系统上测试所有功能
3. **性能分析**: 使用`--debug`选项获取详细信息

## 部署指南

### 用户部署
1. 下载`ChineseToPinyinRenamer_v1.0.0.zip`
2. 解压到任意目录
3. 双击`启动工具.bat`开始使用

### 企业部署
1. 可通过网络共享分发
2. 支持静默安装（复制文件即可）
3. 无需管理员权限（除非处理系统文件）

### 更新部署
1. 替换exe文件即可
2. 保留用户配置和日志
3. 向后兼容旧版本处理的文件

## 许可证

本项目使用MIT许可证，允许自由使用、修改和分发。

## 技术支持

如有构建问题，请检查：
1. Python版本兼容性
2. 依赖库版本
3. 系统环境变量
4. 防病毒软件设置
