中文转拼音批量重命名工具 - 使用说明

这是一个独立的可执行文件，无需安装Python即可运行。

使用方法：

1. 命令行模式：
   ChineseToPinyinRenamer.exe "目标文件夹路径"
   
   例如：
   ChineseToPinyinRenamer.exe "C:\Users\<USER>\Documents\测试文件夹"

2. 交互式模式：
   ChineseToPinyinRenamer.exe --interactive
   
3. 查看帮助：
   ChineseToPinyinRenamer.exe --help

注意事项：
- 请在运行前备份重要文件
- 程序会直接修改文件和文件夹名称，操作不可逆
- 支持Windows 7及以上版本
- 程序会自动生成处理报告

功能说明：
- 递归处理指定目录及其所有子目录
- 将文件夹名称中的中文转换为拼音
- 将文件名称中的中文转换为拼音
- 处理txt文件内容，将中文转换为拼音
- 中文标点符号转换为英文标点符号
- 自动处理重名冲突
- 完善的错误处理和统计报告

技术支持：
如有问题，请查看README.md文件获取更多信息。
