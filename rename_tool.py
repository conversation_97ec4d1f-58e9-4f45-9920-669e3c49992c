#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文转拼音批量重命名工具
主程序入口

功能：
1. 递归遍历指定根目录及其所有子目录
2. 将所有文件夹名称中的中文字符转换为对应的拼音
3. 将所有文件名称中的中文字符转换为对应的拼音
4. 读取所有.txt文件的内容，将文件内容中的中文字符替换为对应的拼音
5. 将中文标点符号替换为对应的英文标点符号

兼容Python 3.6及以上版本
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# 导入自定义模块
from file_renamer import FileRenamer
from txt_processor import TxtProcessor
from statistics_manager import StatisticsManager


class ChineseToPinyinRenamer:
    """中文转拼音重命名工具主类"""
    
    def __init__(self):
        """初始化工具"""
        self.file_renamer = FileRenamer()
        self.txt_processor = TxtProcessor()
        self.stats_manager = StatisticsManager()
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def validate_path(self, path):
        """
        验证路径是否有效
        
        Args:
            path (str): 要验证的路径
            
        Returns:
            tuple: (是否有效, 绝对路径)
        """
        try:
            abs_path = os.path.abspath(path)
            
            if not os.path.exists(abs_path):
                self.logger.error(f"路径不存在: {abs_path}")
                return False, abs_path
            
            if not os.path.isdir(abs_path):
                self.logger.error(f"路径不是目录: {abs_path}")
                return False, abs_path
            
            return True, abs_path
            
        except Exception as e:
            self.logger.error(f"路径验证失败: {str(e)}")
            return False, path
    
    def process_directory(self, target_path, generate_report=True):
        """
        处理指定目录
        
        Args:
            target_path (str): 目标目录路径
            generate_report (bool): 是否生成报告文件
            
        Returns:
            bool: 处理是否成功
        """
        # 验证路径
        is_valid, abs_path = self.validate_path(target_path)
        if not is_valid:
            return False
        
        self.logger.info(f"开始处理目录: {abs_path}")
        
        # 开始统计
        self.stats_manager.start_processing()
        
        try:
            # 1. 处理文件和文件夹重命名
            self.logger.info("步骤 1/2: 处理文件和文件夹重命名...")
            file_stats = self.file_renamer.process_directory(abs_path)
            self.stats_manager.merge_file_renamer_stats(file_stats)
            
            # 2. 处理txt文件内容
            self.logger.info("步骤 2/2: 处理txt文件内容...")
            txt_stats = self.txt_processor.process_directory(abs_path)
            self.stats_manager.merge_txt_processor_stats(txt_stats)
            
            # 结束统计
            self.stats_manager.end_processing()
            
            # 打印结果摘要
            self.stats_manager.print_summary()
            
            # 生成报告文件
            if generate_report:
                report_filename = f"rename_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                report_path = os.path.join(abs_path, report_filename)
                self.stats_manager.save_report(report_path)
            
            self.logger.info("处理完成！")
            return True
            
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {str(e)}")
            self.stats_manager.add_error("处理错误", str(e))
            return False
    
    def run_interactive(self):
        """运行交互式模式"""
        print("中文转拼音批量重命名工具")
        print("="*50)
        
        while True:
            try:
                target_path = input("\n请输入目标文件夹路径 (输入 'quit' 退出): ").strip()
                
                if target_path.lower() in ['quit', 'exit', 'q']:
                    print("退出程序。")
                    break
                
                if not target_path:
                    print("请输入有效的路径。")
                    continue
                
                # 询问是否生成报告
                generate_report = input("是否生成处理报告？(y/n，默认y): ").strip().lower()
                generate_report = generate_report != 'n'
                
                # 确认处理
                print(f"\n即将处理目录: {target_path}")
                print("注意：此操作将修改文件和文件夹名称，以及txt文件内容。")
                confirm = input("确认继续？(y/n): ").strip().lower()
                
                if confirm == 'y':
                    success = self.process_directory(target_path, generate_report)
                    if success:
                        print("\n处理成功完成！")
                    else:
                        print("\n处理过程中出现错误，请查看日志。")
                else:
                    print("操作已取消。")
                
                # 重置统计信息，准备下次处理
                self.file_renamer.reset_statistics()
                self.txt_processor.reset_statistics()
                self.stats_manager.reset()
                
            except KeyboardInterrupt:
                print("\n\n用户中断操作，退出程序。")
                break
            except Exception as e:
                print(f"\n发生错误: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='中文转拼音批量重命名工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python rename_tool.py /path/to/directory          # 处理指定目录
  python rename_tool.py /path/to/directory --no-report  # 处理但不生成报告
  python rename_tool.py --interactive               # 交互式模式
        """
    )
    
    parser.add_argument(
        'path',
        nargs='?',
        help='要处理的目标目录路径'
    )
    
    parser.add_argument(
        '--no-report',
        action='store_true',
        help='不生成处理报告文件'
    )
    
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='运行交互式模式'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志信息'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建工具实例
    renamer = ChineseToPinyinRenamer()
    
    try:
        if args.interactive:
            # 交互式模式
            renamer.run_interactive()
        elif args.path:
            # 命令行模式
            generate_report = not args.no_report
            success = renamer.process_directory(args.path, generate_report)
            sys.exit(0 if success else 1)
        else:
            # 没有提供参数，显示帮助信息
            parser.print_help()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断操作。")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
