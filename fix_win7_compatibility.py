#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Windows 7兼容性问题修复脚本
解决api-ms-win-core-path-l1-1-0.dll等DLL缺失问题
"""

import os
import sys
import subprocess
import shutil
import urllib.request
from pathlib import Path


def check_system_info():
    """检查系统信息"""
    print("系统信息检查:")
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    
    try:
        import platform
        print(f"系统: {platform.system()} {platform.release()}")
        print(f"架构: {platform.architecture()}")
    except:
        pass


def solution_1_use_older_python():
    """解决方案1: 使用较旧的Python版本重新构建"""
    print("\n解决方案1: 使用Python 3.6-3.8重新构建")
    print("=" * 50)
    print("问题原因: Python 3.9+使用了Windows 7不支持的新API")
    print("解决方法:")
    print("1. 安装Python 3.8.10 (推荐)")
    print("2. 使用Python 3.8重新构建exe文件")
    print("3. Python 3.8下载地址: https://www.python.org/downloads/release/python-3810/")
    
    current_version = sys.version_info
    if current_version.minor <= 8:
        print(f"✓ 当前Python版本 {current_version.major}.{current_version.minor} 兼容Windows 7")
        return True
    else:
        print(f"✗ 当前Python版本 {current_version.major}.{current_version.minor} 可能不兼容Windows 7")
        return False


def solution_2_add_missing_dlls():
    """解决方案2: 手动添加缺失的DLL文件"""
    print("\n解决方案2: 手动添加缺失的DLL文件")
    print("=" * 50)
    
    missing_dlls = [
        "api-ms-win-core-path-l1-1-0.dll",
        "api-ms-win-core-file-l1-2-0.dll", 
        "api-ms-win-core-localization-l1-2-0.dll",
        "api-ms-win-core-processthreads-l1-1-1.dll",
        "api-ms-win-core-file-l2-1-0.dll"
    ]
    
    print("缺失的DLL文件:")
    for dll in missing_dlls:
        print(f"  • {dll}")
    
    print("\n获取DLL文件的方法:")
    print("1. 从Windows 10系统复制 (推荐)")
    print("2. 安装Microsoft Visual C++ Redistributable")
    print("3. 使用Windows 7 API兼容包")
    
    # 检查是否有现成的DLL文件
    dll_dir = Path("win7_dlls")
    if dll_dir.exists():
        print(f"\n✓ 找到DLL目录: {dll_dir}")
        return True
    else:
        print(f"\n创建DLL目录: {dll_dir}")
        dll_dir.mkdir(exist_ok=True)
        return False


def solution_3_install_redistributable():
    """解决方案3: 安装Visual C++ Redistributable"""
    print("\n解决方案3: 安装Microsoft Visual C++ Redistributable")
    print("=" * 50)
    print("下载并安装以下组件:")
    print("1. Microsoft Visual C++ 2015-2019 Redistributable (x64)")
    print("   下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe")
    print("2. Microsoft Visual C++ 2013 Redistributable (x64)")
    print("   下载地址: https://aka.ms/highdpimfc2013x64enu")
    print("3. .NET Framework 4.7.2 或更高版本")
    
    print("\n安装步骤:")
    print("1. 下载上述文件")
    print("2. 以管理员权限运行安装程序")
    print("3. 重启计算机")
    print("4. 重新测试exe文件")


def solution_4_build_with_compatibility():
    """解决方案4: 使用兼容性选项重新构建"""
    print("\n解决方案4: 使用Windows 7兼容性选项重新构建")
    print("=" * 50)
    
    if not Path("build_exe_win7.py").exists():
        print("✗ 缺少Windows 7构建脚本")
        return False
    
    print("使用专用的Windows 7构建脚本:")
    print("python build_exe_win7.py")
    
    response = input("是否现在运行Windows 7构建脚本? (y/n): ").strip().lower()
    if response == 'y':
        try:
            subprocess.check_call([sys.executable, "build_exe_win7.py"])
            print("✓ Windows 7兼容构建完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ 构建失败: {e}")
            return False
    
    return True


def solution_5_create_portable_version():
    """解决方案5: 创建便携版本"""
    print("\n解决方案5: 创建便携版本 (目录模式)")
    print("=" * 50)
    print("使用--onedir模式而不是--onefile模式")
    print("这样可以避免一些DLL加载问题")
    
    try:
        cmd = [
            "pyinstaller",
            "--clean",
            "--onedir",  # 使用目录模式
            "--console",
            "--name=ChineseToPinyinRenamer_Portable",
            "--add-data=README.md;.",
            "--hidden-import=pypinyin",
            "--hidden-import=chardet",
            "--exclude-module=tkinter",
            "--target-architecture=x86_64",
            "--win-private-assemblies",
            "rename_tool.py"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        response = input("是否执行便携版构建? (y/n): ").strip().lower()
        
        if response == 'y':
            subprocess.check_call(cmd)
            print("✓ 便携版构建完成")
            print("输出目录: dist/ChineseToPinyinRenamer_Portable/")
            return True
    
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False
    
    return True


def create_compatibility_guide():
    """创建兼容性指南"""
    guide_content = """Windows 7兼容性问题解决指南

问题描述：
运行exe文件时提示缺少以下DLL文件：
• api-ms-win-core-path-l1-1-0.dll
• api-ms-win-core-file-l1-2-0.dll
• 其他api-ms-win-core-*.dll文件

原因分析：
这些DLL文件是Windows 10引入的新API，Windows 7系统中不存在。
使用较新版本的Python (3.9+) 构建的exe文件会依赖这些API。

解决方案（按推荐顺序）：

方案1: 重新构建 (推荐)
1. 使用Python 3.6-3.8版本重新构建exe文件
2. 运行: python build_exe_win7.py
3. 使用生成的Windows 7兼容版本

方案2: 安装运行库
1. 下载并安装Microsoft Visual C++ 2015-2019 Redistributable (x64)
   下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe
2. 重启计算机后重新测试

方案3: 使用便携版
1. 运行: python fix_win7_compatibility.py
2. 选择创建便携版本
3. 使用目录模式的可执行文件

方案4: 手动添加DLL (高级用户)
1. 从Windows 10系统复制缺失的DLL文件
2. 将DLL文件放在exe文件同一目录
3. 注意：此方法可能违反许可协议

方案5: 升级系统
1. 升级到Windows 10或Windows 11
2. 现代操作系统包含所有必要的API

测试步骤：
1. 在Windows 7系统上测试exe文件
2. 如果仍有问题，检查事件查看器中的错误详情
3. 确保系统已安装所有Windows更新

技术支持：
如果上述方案都无法解决问题，请提供以下信息：
• Windows 7版本和Service Pack
• 错误的完整信息
• 系统事件日志中的相关错误
"""
    
    with open("Windows7兼容性指南.txt", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✓ 创建兼容性指南: Windows7兼容性指南.txt")


def main():
    """主函数"""
    print("Windows 7兼容性问题修复工具")
    print("=" * 50)
    print("解决api-ms-win-core-path-l1-1-0.dll等DLL缺失问题")
    print("=" * 50)
    
    check_system_info()
    
    print("\n可用的解决方案:")
    print("1. 使用较旧的Python版本重新构建 (推荐)")
    print("2. 手动添加缺失的DLL文件")
    print("3. 安装Visual C++ Redistributable")
    print("4. 使用Windows 7兼容性选项构建")
    print("5. 创建便携版本")
    print("6. 创建兼容性指南")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择解决方案 (0-6): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                solution_1_use_older_python()
            elif choice == '2':
                solution_2_add_missing_dlls()
            elif choice == '3':
                solution_3_install_redistributable()
            elif choice == '4':
                solution_4_build_with_compatibility()
            elif choice == '5':
                solution_5_create_portable_version()
            elif choice == '6':
                create_compatibility_guide()
            else:
                print("无效选择，请输入0-6之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n用户中断操作")
            break
        except Exception as e:
            print(f"执行过程中出现错误: {e}")
    
    print("\n修复工具结束")


if __name__ == "__main__":
    main()
