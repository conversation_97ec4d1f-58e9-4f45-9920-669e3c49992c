# -*- coding: utf-8 -*-
"""
中文转拼音核心功能模块
兼容Python 3.6及以上版本
"""

import re
from pypinyin import pinyin, Style


class PinyinConverter:
    """中文转拼音转换器"""
    
    def __init__(self):
        """初始化转换器"""
        # 中文标点符号到英文标点符号的映射
        self.punctuation_map = {
            '，': ',',
            '。': '.',
            '？': '?',
            '！': '!',
            '；': ';',
            '：': ':',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '《': '<',
            '》': '>',
            '、': ',',
            '…': '...',
            '——': '--',
            '－': '-',
            '·': '.',
            '￥': '$',
        }
    
    def convert_chinese_to_pinyin(self, text):
        """
        将中文字符转换为拼音

        Args:
            text (str): 包含中文的文本

        Returns:
            str: 转换后的拼音文本
        """
        if not text:
            return text

        # 先转换标点符号
        result = self.convert_punctuation(text)

        # 在中文字符前后添加分隔符，以便后续处理
        # 匹配中文字符与非中文字符的边界
        result = re.sub(r'([\u4e00-\u9fff])([^\u4e00-\u9fff\s])', r'\1 \2', result)
        result = re.sub(r'([^\u4e00-\u9fff\s])([\u4e00-\u9fff])', r'\1 \2', result)

        # 使用正则表达式找到所有中文字符
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')

        def replace_chinese(match):
            chinese_text = match.group()
            # 转换为拼音，使用普通风格，不带声调
            pinyin_list = pinyin(chinese_text, style=Style.NORMAL, heteronym=False)
            # 将拼音列表转换为字符串，用空格分隔
            return ' '.join([item[0] for item in pinyin_list])

        # 替换所有中文字符为拼音
        result = chinese_pattern.sub(replace_chinese, result)

        # 处理标点符号周围的空格，确保标点符号前后有空格
        # 但要避免在行首行末添加空格
        punctuation_chars = ',.:;!?()[]<>-'
        for punct in punctuation_chars:
            # 在标点符号前后添加空格（如果还没有的话）
            result = re.sub(f'([^\\s]){re.escape(punct)}', f'\\1 {punct}', result)
            result = re.sub(f'{re.escape(punct)}([^\\s])', f'{punct} \\1', result)

        # 清理多余的空格
        result = re.sub(r'\s+', ' ', result).strip()

        return result
    
    def convert_punctuation(self, text):
        """
        将中文标点符号转换为英文标点符号
        
        Args:
            text (str): 包含中文标点的文本
            
        Returns:
            str: 转换后的文本
        """
        if not text:
            return text
            
        result = text
        for chinese_punct, english_punct in self.punctuation_map.items():
            result = result.replace(chinese_punct, english_punct)
        
        return result
    
    def convert_filename(self, filename):
        """
        转换文件名，保持文件扩展名不变
        
        Args:
            filename (str): 原始文件名
            
        Returns:
            str: 转换后的文件名
        """
        if not filename:
            return filename
        
        # 分离文件名和扩展名
        if '.' in filename:
            name_part, ext_part = filename.rsplit('.', 1)
            converted_name = self.convert_chinese_to_pinyin(name_part)
            # 文件名中的空格替换为下划线，避免文件系统问题
            converted_name = converted_name.replace(' ', '_')
            return f"{converted_name}.{ext_part}"
        else:
            converted_name = self.convert_chinese_to_pinyin(filename)
            return converted_name.replace(' ', '_')
    
    def convert_folder_name(self, folder_name):
        """
        转换文件夹名称
        
        Args:
            folder_name (str): 原始文件夹名称
            
        Returns:
            str: 转换后的文件夹名称
        """
        if not folder_name:
            return folder_name
            
        converted_name = self.convert_chinese_to_pinyin(folder_name)
        # 文件夹名中的空格替换为下划线
        return converted_name.replace(' ', '_')
    
    def is_chinese_present(self, text):
        """
        检查文本中是否包含中文字符
        
        Args:
            text (str): 要检查的文本
            
        Returns:
            bool: 如果包含中文字符返回True，否则返回False
        """
        if not text:
            return False
            
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))


# 创建全局转换器实例
converter = PinyinConverter()


def convert_text_to_pinyin(text):
    """
    便捷函数：将文本转换为拼音
    
    Args:
        text (str): 要转换的文本
        
    Returns:
        str: 转换后的拼音文本
    """
    return converter.convert_chinese_to_pinyin(text)


def convert_filename_to_pinyin(filename):
    """
    便捷函数：将文件名转换为拼音
    
    Args:
        filename (str): 要转换的文件名
        
    Returns:
        str: 转换后的文件名
    """
    return converter.convert_filename(filename)


def convert_folder_name_to_pinyin(folder_name):
    """
    便捷函数：将文件夹名转换为拼音
    
    Args:
        folder_name (str): 要转换的文件夹名
        
    Returns:
        str: 转换后的文件夹名
    """
    return converter.convert_folder_name(folder_name)


def has_chinese_characters(text):
    """
    便捷函数：检查文本是否包含中文字符
    
    Args:
        text (str): 要检查的文本
        
    Returns:
        bool: 包含中文字符返回True
    """
    return converter.is_chinese_present(text)
