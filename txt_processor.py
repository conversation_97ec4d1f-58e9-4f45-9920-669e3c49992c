# -*- coding: utf-8 -*-
"""
txt文件内容处理功能模块
兼容Python 3.6及以上版本
"""

import os
import logging
import chardet
from pinyin_converter import convert_text_to_pinyin, has_chinese_characters


class TxtProcessor:
    """txt文件内容处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.stats = {
            'txt_files_processed': 0,
            'txt_files_skipped': 0,
            'errors': 0
        }
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def detect_encoding(self, file_path):
        """
        检测文件编码
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 检测到的编码，默认为utf-8
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                # 如果置信度太低，使用utf-8作为默认编码
                if confidence < 0.7:
                    encoding = 'utf-8'
                
                self.logger.debug(f"文件 {file_path} 检测编码: {encoding} (置信度: {confidence})")
                return encoding
                
        except Exception as e:
            self.logger.warning(f"编码检测失败 {file_path}: {str(e)}，使用默认编码utf-8")
            return 'utf-8'
    
    def read_file_content(self, file_path):
        """
        读取文件内容，自动处理编码问题
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            tuple: (是否成功, 文件内容, 使用的编码)
        """
        # 首先尝试检测编码
        detected_encoding = self.detect_encoding(file_path)
        
        # 尝试多种编码读取文件
        encodings_to_try = [detected_encoding, 'utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']
        
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    self.logger.debug(f"成功使用编码 {encoding} 读取文件: {file_path}")
                    return True, content, encoding
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.logger.error(f"读取文件失败 {file_path} (编码: {encoding}): {str(e)}")
                continue
        
        self.logger.error(f"无法读取文件 {file_path}，尝试了所有编码")
        return False, "", "utf-8"
    
    def write_file_content(self, file_path, content, encoding='utf-8'):
        """
        写入文件内容
        
        Args:
            file_path (str): 文件路径
            content (str): 文件内容
            encoding (str): 编码格式
            
        Returns:
            bool: 是否成功
        """
        try:
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            self.logger.error(f"写入文件失败 {file_path}: {str(e)}")
            return False
    
    def process_txt_file(self, file_path):
        """
        处理单个txt文件
        
        Args:
            file_path (str): txt文件路径
            
        Returns:
            bool: 是否成功处理
        """
        try:
            # 检查文件扩展名
            if not file_path.lower().endswith('.txt'):
                return False
            
            # 读取文件内容
            success, content, original_encoding = self.read_file_content(file_path)
            if not success:
                self.stats['errors'] += 1
                return False
            
            # 检查是否包含中文字符
            if not has_chinese_characters(content):
                self.logger.debug(f"文件不包含中文字符，跳过: {file_path}")
                self.stats['txt_files_skipped'] += 1
                return True
            
            # 转换内容
            converted_content = convert_text_to_pinyin(content)
            
            # 写回文件（使用UTF-8编码确保兼容性）
            if self.write_file_content(file_path, converted_content, 'utf-8'):
                self.logger.info(f"txt文件内容已转换: {file_path}")
                self.stats['txt_files_processed'] += 1
                return True
            else:
                self.stats['errors'] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"处理txt文件失败 {file_path}: {str(e)}")
            self.stats['errors'] += 1
            return False
    
    def process_directory(self, root_path):
        """
        递归处理目录中的所有txt文件
        
        Args:
            root_path (str): 根目录路径
            
        Returns:
            dict: 处理统计信息
        """
        if not os.path.exists(root_path):
            self.logger.error(f"目录不存在: {root_path}")
            return self.stats
        
        if not os.path.isdir(root_path):
            self.logger.error(f"路径不是目录: {root_path}")
            return self.stats
        
        self.logger.info(f"开始处理目录中的txt文件: {root_path}")
        
        # 收集所有txt文件
        txt_files = []
        for root, dirs, files in os.walk(root_path):
            for file_name in files:
                if file_name.lower().endswith('.txt'):
                    file_path = os.path.join(root, file_name)
                    txt_files.append(file_path)
        
        self.logger.info(f"找到 {len(txt_files)} 个txt文件")
        
        # 处理所有txt文件
        for txt_file in txt_files:
            self.process_txt_file(txt_file)
        
        self.logger.info("txt文件处理完成")
        return self.stats
    
    def get_statistics(self):
        """
        获取处理统计信息
        
        Returns:
            dict: 统计信息
        """
        return self.stats.copy()
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'txt_files_processed': 0,
            'txt_files_skipped': 0,
            'errors': 0
        }
