中文转拼音批量重命名工具 v1.0.0
==================================================

这是一个独立的可执行文件，无需安装Python即可运行。
兼容Windows 7及以上版本。

功能特性：
• 递归处理指定目录及其所有子目录
• 将文件夹名称中的中文转换为拼音
• 将文件名称中的中文转换为拼音  
• 处理txt文件内容，将中文转换为拼音
• 中文标点符号转换为英文标点符号
• 自动处理重名冲突
• 完善的错误处理和统计报告
• 支持多种文件编码格式

使用方法：

1. 命令行模式：
   ChineseToPinyinRenamer.exe "目标文件夹路径"
   
   示例：
   ChineseToPinyinRenamer.exe "C:\Users\<USER>\Documents\测试文件夹"

2. 交互式模式：
   ChineseToPinyinRenamer.exe --interactive
   
3. 不生成报告：
   ChineseToPinyinRenamer.exe "目标路径" --no-report
   
4. 显示详细日志：
   ChineseToPinyinRenamer.exe "目标路径" --verbose
   
5. 查看帮助：
   ChineseToPinyinRenamer.exe --help

转换示例：

文件夹名：
• 我的文档 → wo_de_wen_dang
• 图片文件夹 → tu_pian_wen_jian_jia

文件名：
• 个人简历.txt → ge_ren_jian_li.txt
• 测试文件.docx → ce_shi_wen_jian.docx

文件内容（txt文件）：
• 你好，世界！ → ni_hao,shi_jie!
• 姓名：张三 → xing_ming:zhang_san

注意事项：
⚠️ 请在运行前备份重要文件
⚠️ 程序会直接修改文件和文件夹名称，操作不可逆
⚠️ 建议先在测试目录中验证效果

✅ 程序会自动处理编码问题
✅ 遇到重名冲突时会自动添加数字后缀
✅ 单个文件失败不会影响整体处理
✅ 生成详细的处理报告

版本信息：
版本：1.0.0
构建日期：2025-08-22
兼容性：Windows 7/8/10/11

技术支持：
如有问题，请查看README.md文件获取更多信息。
