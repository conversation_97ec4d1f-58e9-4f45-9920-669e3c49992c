#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级exe构建脚本，包含版本信息和图标
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import tempfile

# 版本信息
VERSION = "1.0.0"
DESCRIPTION = "中文转拼音批量重命名工具"
COMPANY = "Open Source"
COPYRIGHT = "Copyright (c) 2025"

def create_version_file():
    """创建版本信息文件"""
    version_info = f"""# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
# filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
# Set not needed items to zero 0.
filevers=(1,0,0,0),
prodvers=(1,0,0,0),
# Contains a bitmask that specifies the valid bits 'flags'r
mask=0x3f,
# Contains a bitmask that specifies the Boolean attributes of the file.
flags=0x0,
# The operating system for which this file was designed.
# 0x4 - NT and there is no need to change it.
OS=0x4,
# The general type of file.
# 0x1 - the file is an application.
fileType=0x1,
# The function of the file.
# 0x0 - the function is not defined for this fileType
subtype=0x0,
# Creation date and time stamp.
date=(0, 0)
),
  kids=[
StringFileInfo(
  [
  StringTable(
    u'040904B0',
    [StringStruct(u'CompanyName', u'{COMPANY}'),
    StringStruct(u'FileDescription', u'{DESCRIPTION}'),
    StringStruct(u'FileVersion', u'{VERSION}'),
    StringStruct(u'InternalName', u'ChineseToPinyinRenamer'),
    StringStruct(u'LegalCopyright', u'{COPYRIGHT}'),
    StringStruct(u'OriginalFilename', u'ChineseToPinyinRenamer.exe'),
    StringStruct(u'ProductName', u'{DESCRIPTION}'),
    StringStruct(u'ProductVersion', u'{VERSION}')])
  ]), 
VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""
    
    version_content = version_info.format(
        VERSION=VERSION,
        DESCRIPTION=DESCRIPTION,
        COMPANY=COMPANY,
        COPYRIGHT=COPYRIGHT
    )
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_content)
    
    print("✓ 版本信息文件创建成功")
    return True

def create_simple_icon():
    """创建一个简单的文本图标（如果PIL不可用）"""
    # 这里我们创建一个简单的批处理文件来生成图标
    # 实际项目中建议使用专业的图标文件
    print("ℹ 使用默认图标（建议后续添加自定义图标）")
    return True

def build_advanced_executable():
    """构建高级可执行文件"""
    print("开始高级构建...")
    
    # 创建版本信息文件
    create_version_file()
    
    # 尝试创建图标
    icon_param = ""
    if os.path.exists("icon.ico"):
        icon_param = "--icon=icon.ico"
        print("✓ 使用自定义图标")
    else:
        create_simple_icon()
    
    # 清理之前的构建文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 清理{dir_name}目录")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--clean",
        "--onefile",
        "--console",
        "--name=ChineseToPinyinRenamer",
        "--version-file=version_info.txt",
        "--add-data=README.md;.",
        "--hidden-import=pypinyin",
        "--hidden-import=pypinyin.contrib",
        "--hidden-import=pypinyin.core",
        "--hidden-import=pypinyin.seg", 
        "--hidden-import=pypinyin.style",
        "--hidden-import=chardet",
        "--hidden-import=chardet.universaldetector",
        "--hidden-import=chardet.charsetprober",
        "--distpath=dist",
        "--workpath=build",
        "--specpath=.",
    ]
    
    if icon_param:
        cmd.append(icon_param)
    
    cmd.append("rename_tool.py")
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ 高级exe文件构建完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False

def create_advanced_release_package():
    """创建高级发布包"""
    print("创建高级发布包...")
    
    # 创建发布目录
    release_dir = Path("release_advanced")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_file = Path("dist/ChineseToPinyinRenamer.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "ChineseToPinyinRenamer.exe")
        print("✓ 复制exe文件")
        
        # 获取文件大小
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.1f} MB")
    else:
        print("✗ exe文件不存在")
        return False
    
    # 复制文档
    docs = ["README.md"]
    for doc in docs:
        if Path(doc).exists():
            shutil.copy2(doc, release_dir / doc)
            print(f"✓ 复制{doc}")
    
    # 创建详细的使用说明
    usage_text = f"""中文转拼音批量重命名工具 v{VERSION}
{'='*50}

这是一个独立的可执行文件，无需安装Python即可运行。
兼容Windows 7及以上版本。

功能特性：
• 递归处理指定目录及其所有子目录
• 将文件夹名称中的中文转换为拼音
• 将文件名称中的中文转换为拼音  
• 处理txt文件内容，将中文转换为拼音
• 中文标点符号转换为英文标点符号
• 自动处理重名冲突
• 完善的错误处理和统计报告
• 支持多种文件编码格式

使用方法：

1. 命令行模式：
   ChineseToPinyinRenamer.exe "目标文件夹路径"
   
   示例：
   ChineseToPinyinRenamer.exe "C:\\Users\\<USER>\\Documents\\测试文件夹"

2. 交互式模式：
   ChineseToPinyinRenamer.exe --interactive
   
3. 不生成报告：
   ChineseToPinyinRenamer.exe "目标路径" --no-report
   
4. 显示详细日志：
   ChineseToPinyinRenamer.exe "目标路径" --verbose
   
5. 查看帮助：
   ChineseToPinyinRenamer.exe --help

转换示例：

文件夹名：
• 我的文档 → wo_de_wen_dang
• 图片文件夹 → tu_pian_wen_jian_jia

文件名：
• 个人简历.txt → ge_ren_jian_li.txt
• 测试文件.docx → ce_shi_wen_jian.docx

文件内容（txt文件）：
• 你好，世界！ → ni_hao,shi_jie!
• 姓名：张三 → xing_ming:zhang_san

注意事项：
⚠️ 请在运行前备份重要文件
⚠️ 程序会直接修改文件和文件夹名称，操作不可逆
⚠️ 建议先在测试目录中验证效果

✅ 程序会自动处理编码问题
✅ 遇到重名冲突时会自动添加数字后缀
✅ 单个文件失败不会影响整体处理
✅ 生成详细的处理报告

版本信息：
版本：{VERSION}
构建日期：2025-08-22
兼容性：Windows 7/8/10/11

技术支持：
如有问题，请查看README.md文件获取更多信息。
"""
    
    with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_text)
    print("✓ 创建详细使用说明")
    
    # 创建增强的批处理文件
    batch_content = f"""@echo off
chcp 65001 >nul
title 中文转拼音批量重命名工具 v{VERSION}
color 0A

echo.
echo     中文转拼音批量重命名工具 v{VERSION}
echo     ========================================
echo.
echo     功能：批量将中文文件名和内容转换为拼音
echo     兼容：Windows 7/8/10/11
echo.
echo     使用方法：
echo     1. 将要处理的文件夹拖拽到此窗口
echo     2. 或直接输入文件夹路径
echo     3. 按回车键开始处理
echo.
echo     注意：请先备份重要文件！
echo.

:INPUT
set /p folder_path="请输入文件夹路径（或拖拽文件夹到此处）: "

if "%folder_path%"=="" (
    echo.
    echo 未输入路径，启动交互模式...
    echo.
    ChineseToPinyinRenamer.exe --interactive
    goto END
)

REM 去除路径两端的引号
set folder_path=%folder_path:"=%

echo.
echo 开始处理文件夹: %folder_path%
echo.
ChineseToPinyinRenamer.exe "%folder_path%"

:END
echo.
echo 处理完成！
echo.
echo 是否继续处理其他文件夹？(y/n)
set /p continue="请选择: "
if /i "%continue%"=="y" (
    echo.
    goto INPUT
)

echo.
echo 感谢使用！按任意键退出...
pause >nul
"""
    
    with open(release_dir / "启动工具.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    print("✓ 创建增强批处理启动文件")
    
    # 创建快速测试脚本
    test_script = f"""@echo off
chcp 65001 >nul
title 快速测试工具

echo 创建测试环境...

REM 创建测试目录
set test_dir=%TEMP%\\pinyin_test_%RANDOM%
mkdir "%test_dir%"
mkdir "%test_dir%\\测试文件夹"
mkdir "%test_dir%\\我的文档"

REM 创建测试文件
echo 你好世界！这是测试内容。 > "%test_dir%\\测试文件夹\\测试文件.txt"
echo 姓名：张三 > "%test_dir%\\我的文档\\个人信息.txt"
echo 项目：中文转拼音工具 >> "%test_dir%\\我的文档\\个人信息.txt"

echo 测试目录创建完成: %test_dir%
echo.
echo 开始测试转换功能...
echo.

ChineseToPinyinRenamer.exe "%test_dir%"

echo.
echo 测试完成！请查看测试目录中的转换结果。
echo 测试目录: %test_dir%
echo.
echo 按任意键打开测试目录...
pause >nul
explorer "%test_dir%"
"""
    
    with open(release_dir / "快速测试.bat", "w", encoding="gbk") as f:
        f.write(test_script)
    print("✓ 创建快速测试脚本")
    
    # 创建卸载说明
    uninstall_text = """卸载说明

本工具是绿色软件，无需安装，删除文件夹即可完全卸载。

卸载步骤：
1. 关闭所有正在运行的程序实例
2. 删除整个工具文件夹
3. 清理临时文件（可选）：
   - 删除 %TEMP% 目录下的临时文件
   - 清空回收站

注意：
- 工具不会在系统中留下注册表项
- 不会安装任何系统服务
- 不会修改系统环境变量
"""
    
    with open(release_dir / "卸载说明.txt", "w", encoding="utf-8") as f:
        f.write(uninstall_text)
    print("✓ 创建卸载说明")
    
    print(f"✓ 高级发布包创建完成，位置: {release_dir.absolute()}")
    return True

def main():
    """主函数"""
    print(f"中文转拼音批量重命名工具 v{VERSION} - 高级exe构建脚本")
    print("=" * 60)
    
    # 检查依赖
    try:
        import pypinyin
        import chardet
        import PyInstaller
        print("✓ 所有依赖已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        return False
    
    # 构建exe
    if not build_advanced_executable():
        return False
    
    # 创建发布包
    if not create_advanced_release_package():
        return False
    
    print("\n" + "=" * 60)
    print("高级构建完成！")
    print(f"版本: {VERSION}")
    print("exe文件位置: dist/ChineseToPinyinRenamer.exe")
    print("发布包位置: release_advanced/")
    print("\n可以将release_advanced文件夹中的内容分发给用户使用。")
    print("该exe文件可在Windows 7及以上版本独立运行。")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中出现错误: {e}")
        sys.exit(1)
