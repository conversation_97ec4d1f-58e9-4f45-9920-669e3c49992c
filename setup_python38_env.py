#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建Python 3.8虚拟环境并构建Windows 7兼容的exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import urllib.request
import zipfile


def check_python38_available():
    """检查系统是否已安装Python 3.8"""
    print("检查Python 3.8可用性...")
    
    # 常见的Python 3.8安装路径
    possible_paths = [
        "python3.8",
        "python38",
        "py -3.8",
        r"C:\Python38\python.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe".format(os.getenv('USERNAME', '')),
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run([path, "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0 and "3.8" in result.stdout:
                print(f"✓ 找到Python 3.8: {path}")
                print(f"  版本: {result.stdout.strip()}")
                return path
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    print("✗ 未找到Python 3.8")
    return None


def download_python38():
    """提供Python 3.8下载指导"""
    print("\nPython 3.8下载指导:")
    print("=" * 50)
    print("请手动下载并安装Python 3.8.10:")
    print("1. 访问: https://www.python.org/downloads/release/python-3810/")
    print("2. 下载: Windows installer (64-bit)")
    print("3. 安装时勾选 'Add Python to PATH'")
    print("4. 安装完成后重新运行此脚本")
    print("\n或者使用以下直接下载链接:")
    print("https://www.python.org/ftp/python/3.8.10/python-3.8.10-amd64.exe")
    
    return False


def create_virtual_environment(python38_path):
    """创建Python 3.8虚拟环境"""
    print("\n创建Python 3.8虚拟环境...")
    
    venv_path = Path("venv_py38")
    
    # 删除现有虚拟环境
    if venv_path.exists():
        print("删除现有虚拟环境...")
        shutil.rmtree(venv_path)
    
    try:
        # 创建虚拟环境
        cmd = [python38_path, "-m", "venv", str(venv_path)]
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ 虚拟环境创建成功")
        
        return venv_path
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 虚拟环境创建失败: {e}")
        return None


def get_venv_python_path(venv_path):
    """获取虚拟环境中的Python路径"""
    if os.name == 'nt':  # Windows
        python_path = venv_path / "Scripts" / "python.exe"
        pip_path = venv_path / "Scripts" / "pip.exe"
    else:  # Unix/Linux
        python_path = venv_path / "bin" / "python"
        pip_path = venv_path / "bin" / "pip"
    
    return python_path, pip_path


def install_dependencies(venv_path):
    """在虚拟环境中安装依赖"""
    print("\n在虚拟环境中安装依赖...")
    
    python_path, pip_path = get_venv_python_path(venv_path)
    
    if not python_path.exists():
        print(f"✗ Python路径不存在: {python_path}")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([str(python_path), "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip升级完成")
        
        # 安装项目依赖
        subprocess.check_call([str(python_path), "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 项目依赖安装完成")
        
        # 验证安装
        result = subprocess.run([str(python_path), "-c", 
                               "import pypinyin, chardet, PyInstaller; print('所有依赖安装成功')"],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ 依赖验证成功")
            return True
        else:
            print(f"✗ 依赖验证失败: {result.stderr}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖安装失败: {e}")
        return False


def build_exe_with_python38(venv_path):
    """使用Python 3.8构建exe文件"""
    print("\n使用Python 3.8构建exe文件...")
    
    python_path, _ = get_venv_python_path(venv_path)
    
    # 清理之前的构建文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 清理{dir_name}目录")
    
    try:
        # 构建命令
        cmd = [
            str(python_path), "-m", "PyInstaller",
            "--clean",
            "--onefile",
            "--console",
            "--name=ChineseToPinyinRenamer_Python38",
            "--add-data=README.md;.",
            # 隐藏导入
            "--hidden-import=pypinyin",
            "--hidden-import=pypinyin.contrib",
            "--hidden-import=pypinyin.core",
            "--hidden-import=pypinyin.seg",
            "--hidden-import=pypinyin.style",
            "--hidden-import=chardet",
            "--hidden-import=chardet.universaldetector",
            "--hidden-import=chardet.charsetprober",
            # 排除模块
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "--exclude-module=pandas",
            "--exclude-module=PIL",
            # 禁用UPX压缩
            "--noupx",
            "rename_tool.py"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ Python 3.8 exe构建完成")
        
        # 检查生成的文件
        exe_file = Path("dist/ChineseToPinyinRenamer_Python38.exe")
        if exe_file.exists():
            file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
            print(f"✓ exe文件生成成功")
            print(f"  文件路径: {exe_file}")
            print(f"  文件大小: {file_size:.1f} MB")
            return True
        else:
            print("✗ exe文件未生成")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False


def create_python38_release_package():
    """创建Python 3.8发布包"""
    print("\n创建Python 3.8发布包...")
    
    # 创建发布目录
    release_dir = Path("release_python38")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_file = Path("dist/ChineseToPinyinRenamer_Python38.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "ChineseToPinyinRenamer.exe")
        print("✓ 复制exe文件")
        
        # 获取文件大小
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.1f} MB")
    else:
        print("✗ exe文件不存在")
        return False
    
    # 复制文档
    if Path("README.md").exists():
        shutil.copy2("README.md", release_dir / "README.md")
        print("✓ 复制README文件")
    
    # 创建Python 3.8版本说明
    version_info = """中文转拼音批量重命名工具 - Python 3.8版本

本版本使用Python 3.8构建，具有最佳的Windows 7兼容性。

版本特点：
• 使用Python 3.8.10构建，完全兼容Windows 7
• 解决了api-ms-win-core-path-l1-1-0.dll等DLL缺失问题
• 文件大小经过优化
• 包含所有必要的运行时库

系统要求：
• Windows 7 SP1 及以上版本
• 64位操作系统
• 至少512MB可用内存

使用方法：
1. 命令行模式：ChineseToPinyinRenamer.exe "目标文件夹路径"
2. 交互式模式：ChineseToPinyinRenamer.exe --interactive
3. 查看帮助：ChineseToPinyinRenamer.exe --help

兼容性说明：
• 本版本在Windows 7上测试通过
• 如果仍有问题，请安装Microsoft Visual C++ 2015-2019 Redistributable
• 下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe

构建信息：
• Python版本: 3.8.10
• PyInstaller版本: 最新兼容版本
• 构建日期: 2025-08-22
• 目标平台: Windows 7/8/10/11 (64位)

注意事项：
⚠️ 请在使用前备份重要文件
⚠️ 程序会直接修改文件和文件夹名称，操作不可逆
⚠️ 建议先在测试目录中验证效果
"""
    
    with open(release_dir / "Python38版本说明.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    print("✓ 创建版本说明")
    
    # 创建批处理启动文件
    batch_content = """@echo off
chcp 65001 >nul
title 中文转拼音批量重命名工具 - Python 3.8版本

echo.
echo     中文转拼音批量重命名工具 - Python 3.8版本
echo     ==========================================
echo.
echo     本版本使用Python 3.8构建，完全兼容Windows 7
echo.

:INPUT
set /p folder_path="请输入文件夹路径（或拖拽文件夹到此处）: "

if "%folder_path%"=="" (
    echo.
    echo 未输入路径，启动交互模式...
    echo.
    ChineseToPinyinRenamer.exe --interactive
    goto END
)

REM 去除路径两端的引号
set folder_path=%folder_path:"=%

echo.
echo 开始处理文件夹: %folder_path%
echo.
ChineseToPinyinRenamer.exe "%folder_path%"

:END
echo.
echo 处理完成！
echo.
echo 是否继续处理其他文件夹？(y/n)
set /p continue="请选择: "
if /i "%continue%"=="y" (
    echo.
    goto INPUT
)

echo.
echo 感谢使用！按任意键退出...
pause >nul
"""
    
    with open(release_dir / "启动工具_Python38.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    print("✓ 创建批处理启动文件")
    
    print(f"✓ Python 3.8发布包创建完成，位置: {release_dir.absolute()}")
    return True


def test_exe_functionality(venv_path):
    """测试exe文件功能"""
    print("\n测试exe文件功能...")
    
    exe_file = Path("dist/ChineseToPinyinRenamer_Python38.exe")
    if not exe_file.exists():
        print("✗ exe文件不存在")
        return False
    
    try:
        # 测试帮助命令
        result = subprocess.run([str(exe_file), "--help"], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✓ 帮助命令测试通过")
        else:
            print(f"✗ 帮助命令测试失败: {result.stderr}")
            return False
        
        # 测试版本信息
        python_path, _ = get_venv_python_path(venv_path)
        version_result = subprocess.run([str(python_path), "--version"], 
                                      capture_output=True, text=True)
        print(f"✓ 构建环境Python版本: {version_result.stdout.strip()}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("✗ exe文件测试超时")
        return False
    except Exception as e:
        print(f"✗ exe文件测试失败: {e}")
        return False


def main():
    """主函数"""
    print("Python 3.8虚拟环境构建脚本")
    print("=" * 50)
    print("本脚本将创建Python 3.8虚拟环境并构建Windows 7兼容的exe文件")
    print("=" * 50)
    
    # 检查Python 3.8
    python38_path = check_python38_available()
    if not python38_path:
        download_python38()
        return False
    
    # 创建虚拟环境
    venv_path = create_virtual_environment(python38_path)
    if not venv_path:
        return False
    
    # 安装依赖
    if not install_dependencies(venv_path):
        return False
    
    # 构建exe
    if not build_exe_with_python38(venv_path):
        return False
    
    # 测试exe功能
    if not test_exe_functionality(venv_path):
        print("⚠️  exe文件构建完成，但功能测试失败")
    
    # 创建发布包
    if not create_python38_release_package():
        return False
    
    print("\n" + "=" * 50)
    print("Python 3.8构建完成！")
    print("=" * 50)
    print("虚拟环境位置: venv_py38/")
    print("exe文件位置: dist/ChineseToPinyinRenamer_Python38.exe")
    print("发布包位置: release_python38/")
    print("\n重要提示:")
    print("• 本版本使用Python 3.8构建，具有最佳Windows 7兼容性")
    print("• 建议在实际Windows 7系统上测试")
    print("• 虚拟环境可以保留用于后续开发")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n操作过程中出现错误: {e}")
        sys.exit(1)
