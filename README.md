# 中文转拼音批量重命名工具

一个用于批量将文件夹、文件名和txt文件内容中的中文字符转换为拼音的Python工具。

## 功能特性

- 🔄 递归遍历指定目录及其所有子目录
- 📁 将文件夹名称中的中文字符转换为拼音
- 📄 将文件名称中的中文字符转换为拼音
- 📝 处理txt文件内容，将中文字符替换为拼音
- 🔤 中文标点符号转换为英文标点符号
- ⚡ 处理重名冲突，自动添加数字后缀
- 🛡️ 完善的错误处理机制
- 📊 详细的处理结果统计和报告生成
- 🔧 支持多种编码格式的文件处理
- 💻 提供命令行和交互式两种使用模式

## 系统要求

- Python 3.6 及以上版本

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 命令行模式

```bash
# 处理指定目录
python rename_tool.py /path/to/directory

# 处理目录但不生成报告
python rename_tool.py /path/to/directory --no-report

# 显示详细日志
python rename_tool.py /path/to/directory --verbose
```

### 2. 交互式模式

```bash
python rename_tool.py --interactive
```

### 3. 查看帮助

```bash
python rename_tool.py --help
```

## 使用示例

### 基本使用
```bash
python rename_tool.py ./test_folder
```

### 运行演示
```bash
python example_usage.py
```

### 运行测试
```bash
python test_rename_tool.py
```

## 转换示例

### 文件夹名转换
- `我的文档` → `wo_de_wen_dang`
- `图片文件夹` → `tu_pian_wen_jian_jia`
- `混合Folder` → `hun_he_Folder`

### 文件名转换
- `个人简历.txt` → `ge_ren_jian_li.txt`
- `测试文件.docx` → `ce_shi_wen_jian.docx`
- `Python基础.txt` → `Python_ji_chu.txt`

### 文件内容转换（txt文件）
- `你好，世界！` → `ni_hao,shi_jie!`
- `这是测试内容。` → `zhe_shi_ce_shi_nei_rong.`
- `姓名：张三\n职业：工程师` → `xing_ming:zhang_san\nzhi_ye:gong_cheng_shi`

> **注意**：
> - 文件名和文件夹名中的拼音使用下划线连接
> - txt文件内容转换时：
>   - 中文转换为下划线连接的拼音
>   - 不会自动添加任何空格
>   - 保持原有的换行符和格式
>   - 标点符号保持原样

## 项目结构

```
pinyin2/
├── rename_tool.py          # 主程序入口
├── pinyin_converter.py     # 中文转拼音核心功能
├── file_renamer.py         # 文件和文件夹重命名功能
├── txt_processor.py        # txt文件内容处理功能
├── statistics_manager.py   # 统计和错误处理管理
├── test_rename_tool.py     # 测试用例
├── example_usage.py        # 使用示例
├── requirements.txt        # 依赖配置
└── README.md              # 项目说明
```

## 注意事项

⚠️ **重要提醒**
- 请在运行前备份重要文件
- 程序会直接修改文件和文件夹名称，操作不可逆
- 建议先在测试目录中验证效果

✅ **功能保证**
- 程序会自动处理编码问题，支持UTF-8、GBK等多种编码
- 遇到重名冲突时会自动添加数字后缀避免覆盖
- 完善的错误处理机制，单个文件失败不会影响整体处理
- 生成详细的处理报告，记录所有操作和错误信息

## 技术特点

- 兼容Python 3.6及以上版本
- 使用pypinyin库进行高质量的中文转拼音
- 支持中英文混合文本的智能处理
- 自动检测和处理多种文件编码
- 提供完整的单元测试和集成测试
