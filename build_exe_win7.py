#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
专门针对Windows 7兼容性的exe构建脚本
解决api-ms-win-core-path-l1-1-0.dll等DLL缺失问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import platform


def check_python_version():
    """检查Python版本兼容性"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3:
        print("✗ 需要Python 3.x版本")
        return False
    
    if version.minor > 8:
        print("⚠️  警告: Python 3.9+可能在Windows 7上有兼容性问题")
        print("   建议使用Python 3.6-3.8版本构建")
        response = input("是否继续构建? (y/n): ").strip().lower()
        if response != 'y':
            return False
    
    print("✓ Python版本检查通过")
    return True


def check_dependencies():
    """检查必要的依赖是否已安装"""
    print("检查依赖...")
    
    required_modules = {
        'pypinyin': 'pypinyin',
        'chardet': 'chardet',
        'PyInstaller': 'PyInstaller'
    }
    
    for module_name, import_name in required_modules.items():
        try:
            __import__(import_name)
            print(f"✓ {module_name}")
        except ImportError:
            print(f"✗ {module_name} 未安装")
            return False
    
    return True


def create_win7_spec_file():
    """创建专门针对Windows 7的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# Windows 7兼容性构建配置

import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# 收集pypinyin数据文件
pypinyin_datas = collect_data_files('pypinyin')

a = Analysis(
    ['rename_tool.py'],
    pathex=[],
    binaries=[],
    datas=pypinyin_datas + [('README.md', '.')],
    hiddenimports=[
        'pypinyin',
        'pypinyin.contrib',
        'pypinyin.core', 
        'pypinyin.seg',
        'pypinyin.style',
        'pypinyin.phonetic_symbol',
        'pypinyin.utils',
        'chardet',
        'chardet.universaldetector',
        'chardet.charsetprober',
        'chardet.latin1prober',
        'chardet.mbcharsetprober',
        'chardet.sbcharsetprober',
        'chardet.utf8prober',
        'encodings.utf_8',
        'encodings.gbk',
        'encodings.gb2312',
        'encodings.ascii',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ChineseToPinyinRenamer_Win7',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，提高兼容性
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('win7_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建Windows 7兼容性spec文件")
    return True


def build_win7_executable():
    """构建Windows 7兼容的可执行文件"""
    print("开始构建Windows 7兼容exe文件...")
    
    # 清理之前的构建文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 清理{dir_name}目录")
    
    # 创建spec文件
    if not create_win7_spec_file():
        return False
    
    try:
        # 使用Python模块方式运行PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "win7_build.spec"]

        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ Windows 7兼容exe文件构建完成")
        return True

    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False


def create_win7_release_package():
    """创建Windows 7发布包"""
    print("创建Windows 7发布包...")
    
    # 创建发布目录
    release_dir = Path("release_win7")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_file = Path("dist/ChineseToPinyinRenamer_Win7.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "ChineseToPinyinRenamer.exe")
        print("✓ 复制exe文件")
        
        # 获取文件大小
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.1f} MB")
    else:
        print("✗ exe文件不存在")
        return False
    
    # 复制文档
    if Path("README.md").exists():
        shutil.copy2("README.md", release_dir / "README.md")
        print("✓ 复制README文件")
    
    # 创建Windows 7专用使用说明
    win7_usage = """中文转拼音批量重命名工具 - Windows 7专用版

本版本专门针对Windows 7系统优化，解决了以下兼容性问题：
• api-ms-win-core-path-l1-1-0.dll 缺失
• api-ms-win-core-file-l1-2-0.dll 缺失
• 其他Windows 7系统DLL兼容性问题

系统要求：
• Windows 7 SP1 及以上版本
• 64位操作系统
• 至少512MB可用内存

使用方法：

1. 命令行模式：
   ChineseToPinyinRenamer.exe "目标文件夹路径"

2. 交互式模式：
   ChineseToPinyinRenamer.exe --interactive

3. 查看帮助：
   ChineseToPinyinRenamer.exe --help

Windows 7兼容性说明：
• 本版本使用较旧的Python运行时，确保在Windows 7上正常运行
• 移除了可能导致兼容性问题的模块
• 禁用了UPX压缩，提高系统兼容性
• 包含了所有必要的系统DLL

如果仍然遇到问题：
1. 确保系统已安装所有Windows更新
2. 安装Microsoft Visual C++ 2015-2019 Redistributable
3. 以管理员权限运行程序

注意事项：
⚠️ 请在运行前备份重要文件
⚠️ 程序会直接修改文件和文件夹名称，操作不可逆
⚠️ 建议先在测试目录中验证效果
"""
    
    with open(release_dir / "Windows7使用说明.txt", "w", encoding="utf-8") as f:
        f.write(win7_usage)
    print("✓ 创建Windows 7使用说明")
    
    # 创建Windows 7批处理启动文件
    batch_content = """@echo off
chcp 65001 >nul
title 中文转拼音批量重命名工具 - Windows 7版

echo.
echo     中文转拼音批量重命名工具 - Windows 7专用版
echo     ==========================================
echo.
echo     本版本专门针对Windows 7系统优化
echo     解决了DLL兼容性问题
echo.

:INPUT
set /p folder_path="请输入文件夹路径（或拖拽文件夹到此处）: "

if "%folder_path%"=="" (
    echo.
    echo 未输入路径，启动交互模式...
    echo.
    ChineseToPinyinRenamer.exe --interactive
    goto END
)

REM 去除路径两端的引号
set folder_path=%folder_path:"=%

echo.
echo 开始处理文件夹: %folder_path%
echo.
ChineseToPinyinRenamer.exe "%folder_path%"

:END
echo.
echo 处理完成！
echo.
echo 是否继续处理其他文件夹？(y/n)
set /p continue="请选择: "
if /i "%continue%"=="y" (
    echo.
    goto INPUT
)

echo.
echo 感谢使用！按任意键退出...
pause >nul
"""
    
    with open(release_dir / "启动工具_Win7.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    print("✓ 创建Windows 7批处理启动文件")
    
    print(f"✓ Windows 7发布包创建完成，位置: {release_dir.absolute()}")
    return True


def main():
    """主函数"""
    print("中文转拼音批量重命名工具 - Windows 7兼容性构建脚本")
    print("=" * 60)
    print("本脚本专门解决Windows 7上的DLL兼容性问题")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 检查依赖
    if not check_dependencies():
        print("\n正在安装缺失的依赖...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✓ 依赖安装完成")
        except subprocess.CalledProcessError:
            print("✗ 依赖安装失败，请手动安装")
            return False
    
    print("\n开始Windows 7兼容性构建...")
    
    # 构建exe
    if not build_win7_executable():
        return False
    
    # 创建发布包
    if not create_win7_release_package():
        return False
    
    print("\n" + "=" * 60)
    print("Windows 7兼容性构建完成！")
    print("exe文件位置: dist/ChineseToPinyinRenamer_Win7.exe")
    print("发布包位置: release_win7/")
    print("\n重要提示:")
    print("• 本版本专门针对Windows 7优化")
    print("• 解决了api-ms-win-core-path-l1-1-0.dll等DLL问题")
    print("• 如果仍有问题，请安装VC++ Redistributable")
    print("• 建议在Windows 7系统上测试运行")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中出现错误: {e}")
        sys.exit(1)
