#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建简单的图标文件
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """创建一个简单的图标"""
    try:
        # 创建一个64x64的图像
        size = 64
        img = Image.new('RGBA', (size, size), (70, 130, 180, 255))  # 钢蓝色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制一个简单的"拼"字样式的图标
        # 绘制边框
        draw.rectangle([2, 2, size-3, size-3], outline=(255, 255, 255, 255), width=2)
        
        # 绘制中心的"A"字母代表拼音
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 36)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 计算文字位置
        text = "拼"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 2
        
        # 绘制白色文字
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        
        # 保存为ICO文件
        img.save('icon.ico', format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
        print("✓ 图标文件创建成功: icon.ico")
        return True
        
    except ImportError:
        print("✗ PIL/Pillow 未安装，跳过图标创建")
        return False
    except Exception as e:
        print(f"✗ 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    create_icon()
