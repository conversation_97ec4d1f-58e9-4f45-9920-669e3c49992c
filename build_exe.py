#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
构建exe文件的脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_dependencies():
    """检查必要的依赖是否已安装"""
    print("检查依赖...")
    
    try:
        import pypinyin
        print(f"✓ pypinyin {pypinyin.__version__}")
    except ImportError:
        print("✗ pypinyin 未安装")
        return False
    
    try:
        import chardet
        print(f"✓ chardet {chardet.__version__}")
    except ImportError:
        print("✗ chardet 未安装")
        return False
    
    try:
        import PyInstaller
        print(f"✓ PyInstaller {PyInstaller.__version__}")
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False
    
    return True


def install_dependencies():
    """安装依赖"""
    print("安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖安装失败: {e}")
        return False


def build_executable():
    """构建可执行文件"""
    print("开始构建exe文件...")

    # 清理之前的构建文件
    if os.path.exists("build"):
        shutil.rmtree("build")
        print("✓ 清理build目录")

    if os.path.exists("dist"):
        shutil.rmtree("dist")
        print("✓ 清理dist目录")

    # 使用PyInstaller构建，添加Windows 7兼容性选项
    try:
        cmd = [
            "pyinstaller",
            "--clean",
            "--onefile",
            "--console",
            "--name=ChineseToPinyinRenamer",
            "--add-data=README.md;.",
            "--hidden-import=pypinyin",
            "--hidden-import=pypinyin.contrib",
            "--hidden-import=pypinyin.core",
            "--hidden-import=pypinyin.seg",
            "--hidden-import=pypinyin.style",
            "--hidden-import=chardet",
            "--hidden-import=chardet.universaldetector",
            "--hidden-import=chardet.charsetprober",
            # Windows 7兼容性选项
            "--target-architecture=x86_64",
            "--win-private-assemblies",
            "--win-no-prefer-redirects",
            # 排除可能导致兼容性问题的模块
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "rename_tool.py"
        ]

        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ exe文件构建完成")
        return True

    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False


def create_release_package():
    """创建发布包"""
    print("创建发布包...")
    
    # 创建发布目录
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_file = Path("dist/ChineseToPinyinRenamer.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "ChineseToPinyinRenamer.exe")
        print("✓ 复制exe文件")
    else:
        print("✗ exe文件不存在")
        return False
    
    # 复制README文件
    if Path("README.md").exists():
        shutil.copy2("README.md", release_dir / "README.md")
        print("✓ 复制README文件")
    
    # 创建使用说明
    usage_text = """中文转拼音批量重命名工具 - 使用说明

这是一个独立的可执行文件，无需安装Python即可运行。

使用方法：

1. 命令行模式：
   ChineseToPinyinRenamer.exe "目标文件夹路径"
   
   例如：
   ChineseToPinyinRenamer.exe "C:\\Users\\<USER>\\Documents\\测试文件夹"

2. 交互式模式：
   ChineseToPinyinRenamer.exe --interactive
   
3. 查看帮助：
   ChineseToPinyinRenamer.exe --help

注意事项：
- 请在运行前备份重要文件
- 程序会直接修改文件和文件夹名称，操作不可逆
- 支持Windows 7及以上版本
- 程序会自动生成处理报告

功能说明：
- 递归处理指定目录及其所有子目录
- 将文件夹名称中的中文转换为拼音
- 将文件名称中的中文转换为拼音
- 处理txt文件内容，将中文转换为拼音
- 中文标点符号转换为英文标点符号
- 自动处理重名冲突
- 完善的错误处理和统计报告

技术支持：
如有问题，请查看README.md文件获取更多信息。
"""
    
    with open(release_dir / "使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_text)
    print("✓ 创建使用说明")
    
    # 创建批处理文件，方便用户使用
    batch_content = """@echo off
chcp 65001 >nul
echo 中文转拼音批量重命名工具
echo ========================
echo.
echo 请将要处理的文件夹拖拽到此窗口，然后按回车键
echo 或者直接输入文件夹路径：
echo.
set /p folder_path="文件夹路径: "

if "%folder_path%"=="" (
    echo 未输入路径，启动交互模式...
    ChineseToPinyinRenamer.exe --interactive
) else (
    echo 开始处理文件夹: %folder_path%
    ChineseToPinyinRenamer.exe "%folder_path%"
)

echo.
echo 处理完成，按任意键退出...
pause >nul
"""
    
    with open(release_dir / "启动工具.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    print("✓ 创建批处理启动文件")
    
    print(f"✓ 发布包创建完成，位置: {release_dir.absolute()}")
    return True


def main():
    """主函数"""
    print("中文转拼音批量重命名工具 - exe构建脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n正在安装缺失的依赖...")
        if not install_dependencies():
            print("依赖安装失败，请手动安装")
            return False
    
    print("\n开始构建过程...")
    
    # 构建exe
    if not build_executable():
        print("构建失败")
        return False
    
    # 创建发布包
    if not create_release_package():
        print("发布包创建失败")
        return False
    
    print("\n" + "=" * 50)
    print("构建完成！")
    print("exe文件位置: dist/ChineseToPinyinRenamer.exe")
    print("发布包位置: release/")
    print("\n可以将release文件夹中的内容分发给用户使用。")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中出现错误: {e}")
        sys.exit(1)
