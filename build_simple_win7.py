#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的Windows 7兼容构建脚本
使用直接的PyInstaller命令，避免复杂的spec文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def build_simple_win7_exe():
    """构建简化的Windows 7兼容exe"""
    print("构建简化的Windows 7兼容exe文件...")
    
    # 清理之前的构建文件
    for dir_name in ["build", "dist"]:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 清理{dir_name}目录")
    
    try:
        # 使用简化的PyInstaller命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--onefile",
            "--console",
            "--name=ChineseToPinyinRenamer_Win7_Simple",
            "--add-data=README.md;.",
            # 基本的隐藏导入
            "--hidden-import=pypinyin",
            "--hidden-import=pypinyin.contrib",
            "--hidden-import=pypinyin.core",
            "--hidden-import=pypinyin.seg",
            "--hidden-import=pypinyin.style",
            "--hidden-import=chardet",
            "--hidden-import=chardet.universaldetector",
            "--hidden-import=chardet.charsetprober",
            # 排除不需要的模块以减小体积
            "--exclude-module=tkinter",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy",
            "--exclude-module=scipy",
            "--exclude-module=pandas",
            "--exclude-module=PIL",
            "--exclude-module=cv2",
            # 禁用UPX压缩提高兼容性
            "--noupx",
            "rename_tool.py"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.check_call(cmd)
        print("✓ 简化Windows 7兼容exe构建完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False


def create_win7_simple_package():
    """创建简化的Windows 7发布包"""
    print("创建简化Windows 7发布包...")
    
    # 创建发布目录
    release_dir = Path("release_win7_simple")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制exe文件
    exe_file = Path("dist/ChineseToPinyinRenamer_Win7_Simple.exe")
    if exe_file.exists():
        shutil.copy2(exe_file, release_dir / "ChineseToPinyinRenamer.exe")
        print("✓ 复制exe文件")
        
        # 获取文件大小
        file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.1f} MB")
    else:
        print("✗ exe文件不存在")
        return False
    
    # 创建Windows 7使用说明
    usage_text = """中文转拼音批量重命名工具 - Windows 7简化版

本版本专门为解决Windows 7兼容性问题而创建。

如果遇到DLL缺失错误，请尝试以下解决方案：

方案1: 安装Microsoft Visual C++ Redistributable
1. 下载并安装 Microsoft Visual C++ 2015-2019 Redistributable (x64)
   下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe
2. 重启计算机
3. 重新运行程序

方案2: 以管理员权限运行
1. 右键点击exe文件
2. 选择"以管理员身份运行"

方案3: 兼容性模式
1. 右键点击exe文件
2. 选择"属性"
3. 切换到"兼容性"选项卡
4. 勾选"以兼容模式运行这个程序"
5. 选择"Windows 8"或"Windows 10"

使用方法：
1. 命令行模式：ChineseToPinyinRenamer.exe "目标文件夹路径"
2. 交互式模式：ChineseToPinyinRenamer.exe --interactive
3. 查看帮助：ChineseToPinyinRenamer.exe --help

注意事项：
• 请在使用前备份重要文件
• 程序会直接修改文件和文件夹名称，操作不可逆
• 建议先在测试目录中验证效果

如果问题仍然存在，可能需要：
• 升级到Windows 10或更新版本
• 使用Python源码版本运行程序
"""
    
    with open(release_dir / "Windows7使用说明.txt", "w", encoding="utf-8") as f:
        f.write(usage_text)
    print("✓ 创建Windows 7使用说明")
    
    # 创建批处理启动文件
    batch_content = """@echo off
chcp 65001 >nul
title 中文转拼音批量重命名工具 - Windows 7简化版

echo.
echo     中文转拼音批量重命名工具 - Windows 7简化版
echo     ==========================================
echo.
echo     如果遇到DLL错误，请先安装VC++ Redistributable
echo     下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe
echo.

:INPUT
set /p folder_path="请输入文件夹路径（或拖拽文件夹到此处）: "

if "%folder_path%"=="" (
    echo.
    echo 未输入路径，启动交互模式...
    echo.
    ChineseToPinyinRenamer.exe --interactive
    goto END
)

REM 去除路径两端的引号
set folder_path=%folder_path:"=%

echo.
echo 开始处理文件夹: %folder_path%
echo.
ChineseToPinyinRenamer.exe "%folder_path%"

:END
echo.
echo 处理完成！
echo.
echo 是否继续处理其他文件夹？(y/n)
set /p continue="请选择: "
if /i "%continue%"=="y" (
    echo.
    goto INPUT
)

echo.
echo 感谢使用！按任意键退出...
pause >nul
"""
    
    with open(release_dir / "启动工具.bat", "w", encoding="gbk") as f:
        f.write(batch_content)
    print("✓ 创建批处理启动文件")
    
    # 创建故障排除指南
    troubleshoot_text = """Windows 7兼容性故障排除指南

常见错误及解决方案：

错误1: 缺少api-ms-win-core-path-l1-1-0.dll
解决方案:
1. 安装Microsoft Visual C++ 2015-2019 Redistributable (x64)
2. 下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe
3. 安装后重启计算机

错误2: 程序无法启动
解决方案:
1. 右键exe文件 -> 属性 -> 兼容性
2. 勾选"以兼容模式运行这个程序"
3. 选择"Windows 8"
4. 勾选"以管理员身份运行此程序"

错误3: 程序启动缓慢
解决方案:
1. 关闭杀毒软件的实时监控
2. 将exe文件添加到杀毒软件白名单
3. 确保有足够的磁盘空间

错误4: 处理文件时出错
解决方案:
1. 确保对目标文件夹有写入权限
2. 关闭正在使用目标文件的其他程序
3. 以管理员权限运行程序

系统要求检查：
• Windows 7 SP1 (必需)
• 64位操作系统 (必需)
• 至少512MB可用内存
• 至少100MB磁盘空间

如果所有方案都无效：
1. 考虑升级到Windows 10
2. 使用Python源码版本
3. 联系技术支持
"""
    
    with open(release_dir / "故障排除指南.txt", "w", encoding="utf-8") as f:
        f.write(troubleshoot_text)
    print("✓ 创建故障排除指南")
    
    print(f"✓ 简化Windows 7发布包创建完成，位置: {release_dir.absolute()}")
    return True


def main():
    """主函数"""
    print("中文转拼音批量重命名工具 - 简化Windows 7构建")
    print("=" * 60)
    
    current_version = sys.version_info
    print(f"当前Python版本: {current_version.major}.{current_version.minor}.{current_version.micro}")
    
    if current_version.minor > 8:
        print("⚠️  警告: 当前Python版本可能在Windows 7上有兼容性问题")
        print("   建议使用Python 3.6-3.8版本构建")
    
    # 构建exe
    if not build_simple_win7_exe():
        return False
    
    # 创建发布包
    if not create_win7_simple_package():
        return False
    
    print("\n" + "=" * 60)
    print("简化Windows 7构建完成！")
    print("exe文件位置: dist/ChineseToPinyinRenamer_Win7_Simple.exe")
    print("发布包位置: release_win7_simple/")
    print("\n重要提示:")
    print("• 如果在Windows 7上仍有DLL错误，请安装VC++ Redistributable")
    print("• 下载地址: https://aka.ms/vs/16/release/vc_redist.x64.exe")
    print("• 建议在实际Windows 7系统上测试")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断构建过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n构建过程中出现错误: {e}")
        sys.exit(1)
