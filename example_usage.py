#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
中文转拼音批量重命名工具使用示例
"""

import os
import tempfile
import shutil
from rename_tool import ChineseToPinyinRenamer


def create_example_directory():
    """创建示例目录结构用于演示"""
    # 创建临时目录
    example_dir = tempfile.mkdtemp(prefix="pinyin_rename_example_")
    
    print(f"创建示例目录: {example_dir}")
    
    # 创建中文文件夹
    folders = [
        "我的文档",
        "图片文件夹", 
        "音乐收藏",
        "工作资料/重要文件",
        "学习笔记/Python教程"
    ]
    
    for folder in folders:
        folder_path = os.path.join(example_dir, folder)
        os.makedirs(folder_path, exist_ok=True)
        print(f"创建文件夹: {folder}")
    
    # 创建中文文件和内容
    files = [
        ("我的文档/个人简历.txt", "姓名：张三\n职业：软件工程师\n技能：Python、Java、JavaScript\n联系方式：电话123456789"),
        ("我的文档/购物清单.txt", "今天要买的东西：\n1. 苹果、香蕉、橙子\n2. 牛奶、面包、鸡蛋\n3. 洗发水、牙膏"),
        ("图片文件夹/风景照片说明.txt", "这些照片拍摄于：\n- 北京：天安门、故宫、长城\n- 上海：外滩、东方明珠\n- 杭州：西湖、雷峰塔"),
        ("音乐收藏/歌单.txt", "我最喜欢的歌曲：\n《月亮代表我的心》- 邓丽君\n《青花瓷》- 周杰伦\n《童话》- 光良"),
        ("工作资料/项目计划.txt", "项目名称：中文转拼音工具\n开发周期：2周\n主要功能：文件重命名、内容转换\n技术栈：Python、pypinyin"),
        ("学习笔记/Python基础.txt", "Python学习笔记：\n1. 变量和数据类型\n2. 条件语句和循环\n3. 函数定义和调用\n4. 文件操作和异常处理")
    ]
    
    for file_path, content in files:
        full_path = os.path.join(example_dir, file_path)
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"创建文件: {file_path}")
    
    return example_dir


def demonstrate_tool():
    """演示工具使用"""
    print("中文转拼音批量重命名工具 - 使用示例")
    print("="*60)
    
    # 创建示例目录
    example_dir = create_example_directory()
    
    print(f"\n示例目录创建完成: {example_dir}")
    print("\n原始目录结构:")
    print_directory_structure(example_dir)
    
    # 询问用户是否继续
    print("\n即将演示重命名功能...")
    user_input = input("按回车键继续，或输入 'q' 退出: ").strip()
    
    if user_input.lower() == 'q':
        print("演示取消，清理临时目录...")
        shutil.rmtree(example_dir)
        return
    
    # 使用重命名工具
    print("\n开始处理...")
    renamer = ChineseToPinyinRenamer()
    success = renamer.process_directory(example_dir, generate_report=True)
    
    if success:
        print("\n处理完成！转换后的目录结构:")
        print_directory_structure(example_dir)
        
        print(f"\n示例目录保留在: {example_dir}")
        print("您可以查看转换结果，查看完毕后请手动删除该目录。")
        
        # 显示一个转换后的文件内容示例
        show_converted_file_example(example_dir)
        
    else:
        print("处理失败！")
        shutil.rmtree(example_dir)


def print_directory_structure(root_path, prefix="", max_depth=3, current_depth=0):
    """打印目录结构"""
    if current_depth > max_depth:
        return
        
    try:
        items = sorted(os.listdir(root_path))
        for i, item in enumerate(items):
            item_path = os.path.join(root_path, item)
            is_last = i == len(items) - 1
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(item_path) and current_depth < max_depth:
                next_prefix = prefix + ("    " if is_last else "│   ")
                print_directory_structure(item_path, next_prefix, max_depth, current_depth + 1)
                
    except PermissionError:
        print(f"{prefix}[权限不足]")


def show_converted_file_example(root_path):
    """显示转换后的文件内容示例"""
    print("\n" + "="*60)
    print("文件内容转换示例:")
    print("="*60)
    
    # 查找第一个txt文件
    for root, dirs, files in os.walk(root_path):
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    print(f"文件: {file}")
                    print("-" * 40)
                    print(content[:200] + ("..." if len(content) > 200 else ""))
                    print("="*60)
                    return
                except Exception as e:
                    continue


if __name__ == '__main__':
    try:
        demonstrate_tool()
    except KeyboardInterrupt:
        print("\n\n用户中断演示。")
    except Exception as e:
        print(f"\n演示过程中出现错误: {str(e)}")
