# -*- coding: utf-8 -*-
"""
统计和错误处理管理模块
兼容Python 3.6及以上版本
"""

import logging
import time
from datetime import datetime


class StatisticsManager:
    """统计信息管理器"""
    
    def __init__(self):
        """初始化统计管理器"""
        self.start_time = None
        self.end_time = None
        self.total_stats = {
            'folders_renamed': 0,
            'files_renamed': 0,
            'txt_files_processed': 0,
            'txt_files_skipped': 0,
            'total_errors': 0,
            'processing_time': 0
        }
        self.error_details = []
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def start_processing(self):
        """开始处理，记录开始时间"""
        self.start_time = time.time()
        self.logger.info(f"开始处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def end_processing(self):
        """结束处理，记录结束时间"""
        self.end_time = time.time()
        if self.start_time:
            self.total_stats['processing_time'] = self.end_time - self.start_time
        self.logger.info(f"处理结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    def merge_file_renamer_stats(self, file_stats):
        """
        合并文件重命名器的统计信息
        
        Args:
            file_stats (dict): 文件重命名器的统计信息
        """
        self.total_stats['folders_renamed'] += file_stats.get('folders_renamed', 0)
        self.total_stats['files_renamed'] += file_stats.get('files_renamed', 0)
        self.total_stats['total_errors'] += file_stats.get('errors', 0)
    
    def merge_txt_processor_stats(self, txt_stats):
        """
        合并txt处理器的统计信息
        
        Args:
            txt_stats (dict): txt处理器的统计信息
        """
        self.total_stats['txt_files_processed'] += txt_stats.get('txt_files_processed', 0)
        self.total_stats['txt_files_skipped'] += txt_stats.get('txt_files_skipped', 0)
        self.total_stats['total_errors'] += txt_stats.get('errors', 0)
    
    def add_error(self, error_type, error_message, file_path=None):
        """
        添加错误记录
        
        Args:
            error_type (str): 错误类型
            error_message (str): 错误消息
            file_path (str, optional): 相关文件路径
        """
        error_record = {
            'type': error_type,
            'message': error_message,
            'file_path': file_path,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self.error_details.append(error_record)
        self.total_stats['total_errors'] += 1
        
        # 记录到日志
        if file_path:
            self.logger.error(f"{error_type}: {error_message} (文件: {file_path})")
        else:
            self.logger.error(f"{error_type}: {error_message}")
    
    def get_summary(self):
        """
        获取处理结果摘要
        
        Returns:
            dict: 包含所有统计信息的字典
        """
        summary = self.total_stats.copy()
        summary['error_details'] = self.error_details.copy()
        summary['success_rate'] = self.calculate_success_rate()
        return summary
    
    def calculate_success_rate(self):
        """
        计算成功率
        
        Returns:
            float: 成功率百分比
        """
        total_operations = (
            self.total_stats['folders_renamed'] + 
            self.total_stats['files_renamed'] + 
            self.total_stats['txt_files_processed'] +
            self.total_stats['total_errors']
        )
        
        if total_operations == 0:
            return 100.0
        
        successful_operations = total_operations - self.total_stats['total_errors']
        return (successful_operations / total_operations) * 100
    
    def print_summary(self):
        """打印处理结果摘要"""
        print("\n" + "="*60)
        print("处理结果统计")
        print("="*60)
        
        print(f"重命名文件夹数量: {self.total_stats['folders_renamed']}")
        print(f"重命名文件数量: {self.total_stats['files_renamed']}")
        print(f"处理txt文件数量: {self.total_stats['txt_files_processed']}")
        print(f"跳过txt文件数量: {self.total_stats['txt_files_skipped']}")
        print(f"错误数量: {self.total_stats['total_errors']}")
        
        if self.total_stats['processing_time'] > 0:
            print(f"处理耗时: {self.total_stats['processing_time']:.2f} 秒")
        
        success_rate = self.calculate_success_rate()
        print(f"成功率: {success_rate:.1f}%")
        
        if self.error_details:
            print("\n错误详情:")
            print("-" * 40)
            for i, error in enumerate(self.error_details, 1):
                print(f"{i}. [{error['timestamp']}] {error['type']}: {error['message']}")
                if error['file_path']:
                    print(f"   文件: {error['file_path']}")
        
        print("="*60)
    
    def save_report(self, report_path):
        """
        保存处理报告到文件
        
        Args:
            report_path (str): 报告文件路径
            
        Returns:
            bool: 是否成功保存
        """
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("中文转拼音批量重命名工具 - 处理报告\n")
                f.write("="*60 + "\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("统计信息:\n")
                f.write(f"重命名文件夹数量: {self.total_stats['folders_renamed']}\n")
                f.write(f"重命名文件数量: {self.total_stats['files_renamed']}\n")
                f.write(f"处理txt文件数量: {self.total_stats['txt_files_processed']}\n")
                f.write(f"跳过txt文件数量: {self.total_stats['txt_files_skipped']}\n")
                f.write(f"错误数量: {self.total_stats['total_errors']}\n")
                
                if self.total_stats['processing_time'] > 0:
                    f.write(f"处理耗时: {self.total_stats['processing_time']:.2f} 秒\n")
                
                success_rate = self.calculate_success_rate()
                f.write(f"成功率: {success_rate:.1f}%\n\n")
                
                if self.error_details:
                    f.write("错误详情:\n")
                    f.write("-" * 40 + "\n")
                    for i, error in enumerate(self.error_details, 1):
                        f.write(f"{i}. [{error['timestamp']}] {error['type']}: {error['message']}\n")
                        if error['file_path']:
                            f.write(f"   文件: {error['file_path']}\n")
                
                f.write("\n" + "="*60 + "\n")
            
            self.logger.info(f"处理报告已保存到: {report_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存报告失败: {str(e)}")
            return False
    
    def reset(self):
        """重置所有统计信息"""
        self.start_time = None
        self.end_time = None
        self.total_stats = {
            'folders_renamed': 0,
            'files_renamed': 0,
            'txt_files_processed': 0,
            'txt_files_skipped': 0,
            'total_errors': 0,
            'processing_time': 0
        }
        self.error_details = []
