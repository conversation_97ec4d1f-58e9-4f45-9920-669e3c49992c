('C:\\Users\\<USER>\\Documents\\pinyin2\\dist\\ChineseToPinyinRenamer.exe',
 True,
 False,
 False,
 'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 versioninfo.VSVersionInfo(ffi=versioninfo.FixedFileInfo(filevers=(1, 0, 0, 0), prodvers=(1, 0, 0, 0), mask=0x3f, flags=0x0, OS=0x4, fileType=1, subtype=0x0, date=(0, 0)), kids=[versioninfo.StringFileInfo([versioninfo.StringTable('040904B0', [versioninfo.StringStruct('CompanyName', 'Open Source'), versioninfo.StringStruct('FileDescription', '中文转拼音批量重命名工具'), versioninfo.StringStruct('FileVersion', '1.0.0'), versioninfo.StringStruct('InternalName', 'ChineseToPinyinRenamer'), versioninfo.StringStruct('LegalCopyright', 'Copyright (c) 2025'), versioninfo.StringStruct('OriginalFilename', 'ChineseToPinyinRenamer.exe'), versioninfo.StringStruct('ProductName', '中文转拼音批量重命名工具'), versioninfo.StringStruct('ProductVersion', '1.0.0')])]), versioninfo.VarFileInfo([versioninfo.VarStruct('Translation', [1033, 1200])])]),
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\ChineseToPinyinRenamer.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('rename_tool',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\rename_tool.py',
   'PYSOURCE'),
  ('python312.dll', 'C:\\Users\\<USER>\\anaconda3\\python312.dll', 'BINARY'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\anaconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Users\\<USER>\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\anaconda3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Users\\<USER>\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('README.md', 'C:\\Users\\<USER>\\Documents\\pinyin2\\README.md', 'DATA'),
  ('pypinyin\\utils.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\utils.pyi',
   'DATA'),
  ('pypinyin\\constants.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\constants.pyi',
   'DATA'),
  ('pypinyin\\phrases_dict.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\phrases_dict.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\pinyin_dict.json',
   'DATA'),
  ('pypinyin\\style\\initials.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\initials.pyi',
   'DATA'),
  ('pypinyin\\contrib\\uv.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\uv.pyi',
   'DATA'),
  ('pypinyin\\phonetic_symbol.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\phonetic_symbol.pyi',
   'DATA'),
  ('pypinyin\\seg\\simpleseg.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\seg\\simpleseg.pyi',
   'DATA'),
  ('pypinyin\\__init__.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\cyrillic.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\cyrillic.pyi',
   'DATA'),
  ('pypinyin\\py.typed',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\py.typed',
   'DATA'),
  ('pypinyin\\phrases_dict.json',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\phrases_dict.json',
   'DATA'),
  ('pypinyin\\tools\\toneconvert.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\tools\\toneconvert.pyi',
   'DATA'),
  ('pypinyin\\contrib\\mmseg.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\style\\wadegiles.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\wadegiles.pyi',
   'DATA'),
  ('pypinyin\\style\\gwoyeu.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\gwoyeu.pyi',
   'DATA'),
  ('pypinyin\\style\\braille_mainland.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\braille_mainland.pyi',
   'DATA'),
  ('pypinyin\\style\\__init__.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\__init__.pyi',
   'DATA'),
  ('pypinyin\\style\\tone.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\tone.pyi',
   'DATA'),
  ('pypinyin\\compat.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\compat.pyi',
   'DATA'),
  ('pypinyin\\converter.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\converter.pyi',
   'DATA'),
  ('pypinyin\\pinyin_dict.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\pinyin_dict.pyi',
   'DATA'),
  ('pypinyin\\exceptions.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\exceptions.pyi',
   'DATA'),
  ('pypinyin\\style\\others.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\others.pyi',
   'DATA'),
  ('pypinyin\\style\\_constants.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_constants.pyi',
   'DATA'),
  ('pypinyin\\core.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\core.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_convert.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_tone_convert.pyi',
   'DATA'),
  ('pypinyin\\style\\finals.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\finals.pyi',
   'DATA'),
  ('pypinyin\\style\\_utils.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_utils.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_convert.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\tone_convert.pyi',
   'DATA'),
  ('pypinyin\\contrib\\tone_sandhi.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.pyi',
   'DATA'),
  ('pypinyin\\runner.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\runner.pyi',
   'DATA'),
  ('pypinyin\\contrib\\_tone_rule.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\standard.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\standard.pyi',
   'DATA'),
  ('pypinyin\\seg\\mmseg.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\seg\\mmseg.pyi',
   'DATA'),
  ('pypinyin\\style\\bopomofo.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\bopomofo.pyi',
   'DATA'),
  ('pypinyin\\style\\_tone_rule.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_tone_rule.pyi',
   'DATA'),
  ('pypinyin\\contrib\\neutral_tone.pyi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\neutral_tone.pyi',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1755835318,
 [('run.exe',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\anaconda3\\python312.dll')
