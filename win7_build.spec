# -*- mode: python ; coding: utf-8 -*-
# Windows 7兼容性构建配置

import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# 收集pypinyin数据文件
pypinyin_datas = collect_data_files('pypinyin')

a = Analysis(
    ['rename_tool.py'],
    pathex=[],
    binaries=[],
    datas=pypinyin_datas + [('README.md', '.')],
    hiddenimports=[
        'pypinyin',
        'pypinyin.contrib',
        'pypinyin.core', 
        'pypinyin.seg',
        'pypinyin.style',
        'pypinyin.phonetic_symbol',
        'pypinyin.utils',
        'chardet',
        'chardet.universaldetector',
        'chardet.charsetprober',
        'chardet.latin1prober',
        'chardet.mbcharsetprober',
        'chardet.sbcharsetprober',
        'chardet.utf8prober',
        'encodings.utf_8',
        'encodings.gbk',
        'encodings.gb2312',
        'encodings.ascii',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
        'pandas',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    win_no_prefer_redirects=True,
    win_private_assemblies=True,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ChineseToPinyinRenamer_Win7',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用UPX压缩，提高兼容性
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch='x86_64',
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
