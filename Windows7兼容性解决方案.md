# Windows 7兼容性解决方案

## 问题描述

在Windows 7系统上运行exe文件时，可能会遇到以下错误：
- `api-ms-win-core-path-l1-1-0.dll` 缺失
- `api-ms-win-core-file-l1-2-0.dll` 缺失
- 其他 `api-ms-win-core-*.dll` 文件缺失

## 根本原因

这些DLL文件是Windows 10引入的新API，Windows 7系统中不存在。使用较新版本的Python (3.9+) 构建的exe文件会依赖这些API。

## 解决方案

### 方案1: 使用Windows 7专用版本 (推荐)

我们已经创建了专门针对Windows 7优化的版本：

**文件位置**: `release_win7_simple/ChineseToPinyinRenamer.exe`

**特点**:
- 文件大小: 8.7MB
- 移除了可能导致兼容性问题的模块
- 禁用了UPX压缩提高兼容性
- 包含详细的使用说明和故障排除指南

**使用方法**:
1. 复制整个 `release_win7_simple` 文件夹到Windows 7系统
2. 双击 `启动工具.bat` 开始使用
3. 或直接运行 `ChineseToPinyinRenamer.exe`

### 方案2: 安装Microsoft Visual C++ Redistributable

如果方案1仍然不工作，请安装运行库：

1. **下载地址**: https://aka.ms/vs/16/release/vc_redist.x64.exe
2. **安装步骤**:
   - 以管理员权限运行安装程序
   - 完成安装后重启计算机
   - 重新测试exe文件

### 方案3: 兼容性模式运行

1. 右键点击exe文件，选择"属性"
2. 切换到"兼容性"选项卡
3. 勾选"以兼容模式运行这个程序"
4. 选择"Windows 8"或"Windows 10"
5. 勾选"以管理员身份运行此程序"
6. 点击"确定"

### 方案4: 使用Python源码版本

如果exe版本仍有问题，可以直接使用Python源码：

1. 在Windows 7上安装Python 3.6-3.8
2. 安装依赖: `pip install -r requirements.txt`
3. 运行: `python rename_tool.py`

## 构建脚本说明

### 可用的构建脚本

1. **`build_exe.py`** - 基础构建脚本
2. **`build_exe_advanced.py`** - 高级构建脚本（包含版本信息）
3. **`build_simple_win7.py`** - Windows 7专用简化构建 (推荐)
4. **`fix_win7_compatibility.py`** - 兼容性问题修复工具

### 推荐的构建流程

对于Windows 7兼容性，推荐使用：

```bash
python build_simple_win7.py
```

这将创建：
- `dist/ChineseToPinyinRenamer_Win7_Simple.exe` - 优化的exe文件
- `release_win7_simple/` - 完整的发布包

## 系统要求

### 最低要求
- Windows 7 SP1 (64位)
- 512MB 可用内存
- 100MB 磁盘空间

### 推荐配置
- Windows 7 SP1 或更高版本
- 2GB 内存
- 已安装所有Windows更新
- Microsoft Visual C++ 2015-2019 Redistributable

## 测试建议

### 在Windows 7上测试
1. 在干净的Windows 7系统上测试
2. 测试各种使用场景（命令行、交互式）
3. 测试不同类型的文件和文件夹
4. 验证中文转拼音功能正常

### 测试步骤
1. 复制 `release_win7_simple` 文件夹到Windows 7系统
2. 运行 `启动工具.bat`
3. 创建测试文件夹和文件
4. 验证转换功能
5. 检查生成的报告

## 故障排除

### 常见问题

**问题**: 仍然提示缺少DLL文件
**解决**: 
1. 安装VC++ Redistributable
2. 以管理员权限运行
3. 使用兼容性模式

**问题**: 程序启动缓慢
**解决**:
1. 关闭杀毒软件实时监控
2. 将exe添加到白名单
3. 确保有足够磁盘空间

**问题**: 处理文件时出错
**解决**:
1. 确保有文件夹写入权限
2. 关闭正在使用文件的程序
3. 以管理员权限运行

### 日志分析

如果问题持续存在，可以：
1. 使用 `--verbose` 参数获取详细日志
2. 检查Windows事件查看器
3. 查看生成的错误报告

## 部署建议

### 企业部署
1. 预先在所有Windows 7系统上安装VC++ Redistributable
2. 使用网络共享分发exe文件
3. 提供详细的使用培训

### 个人用户
1. 下载完整的 `release_win7_simple` 包
2. 解压到本地目录
3. 使用批处理文件启动

## 版本兼容性

| Python版本 | Windows 7兼容性 | 推荐程度 |
|-----------|----------------|----------|
| 3.6.x     | ✅ 完全兼容     | ⭐⭐⭐⭐⭐ |
| 3.7.x     | ✅ 完全兼容     | ⭐⭐⭐⭐⭐ |
| 3.8.x     | ✅ 完全兼容     | ⭐⭐⭐⭐   |
| 3.9.x     | ⚠️ 可能有问题   | ⭐⭐⭐    |
| 3.10+     | ❌ 不推荐      | ⭐       |

## 技术支持

如果上述方案都无法解决问题，请提供：
1. Windows 7的具体版本和Service Pack
2. 完整的错误信息截图
3. 系统事件日志中的相关错误
4. 已尝试的解决方案

## 总结

通过使用专门的Windows 7构建脚本和适当的运行库，可以解决大部分兼容性问题。推荐使用 `release_win7_simple` 版本，它经过专门优化，具有最好的Windows 7兼容性。
