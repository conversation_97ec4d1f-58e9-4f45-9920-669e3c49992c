('C:\\Users\\<USER>\\Documents\\pinyin2\\build\\ChineseToPinyinRenamer\\PYZ-00.pyz',
 [('__future__', 'C:\\Users\\<USER>\\anaconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('file_renamer',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\file_renamer.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\anaconda3\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pinyin_converter',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\pinyin_converter.py',
   'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('pypinyin',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\__init__.py',
   'PYMODULE'),
  ('pypinyin.compat',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\compat.py',
   'PYMODULE'),
  ('pypinyin.constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\constants.py',
   'PYMODULE'),
  ('pypinyin.contrib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\__init__.py',
   'PYMODULE'),
  ('pypinyin.contrib._tone_rule',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.contrib.neutral_tone',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\neutral_tone.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\tone_convert.py',
   'PYMODULE'),
  ('pypinyin.contrib.tone_sandhi',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\tone_sandhi.py',
   'PYMODULE'),
  ('pypinyin.contrib.uv',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\contrib\\uv.py',
   'PYMODULE'),
  ('pypinyin.converter',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\converter.py',
   'PYMODULE'),
  ('pypinyin.core',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\core.py',
   'PYMODULE'),
  ('pypinyin.exceptions',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\exceptions.py',
   'PYMODULE'),
  ('pypinyin.phonetic_symbol',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\phonetic_symbol.py',
   'PYMODULE'),
  ('pypinyin.phrases_dict',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\phrases_dict.py',
   'PYMODULE'),
  ('pypinyin.pinyin_dict',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\pinyin_dict.py',
   'PYMODULE'),
  ('pypinyin.seg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\seg\\__init__.py',
   'PYMODULE'),
  ('pypinyin.seg.mmseg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\seg\\mmseg.py',
   'PYMODULE'),
  ('pypinyin.seg.simpleseg',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\seg\\simpleseg.py',
   'PYMODULE'),
  ('pypinyin.standard',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\standard.py',
   'PYMODULE'),
  ('pypinyin.style',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\__init__.py',
   'PYMODULE'),
  ('pypinyin.style._constants',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_constants.py',
   'PYMODULE'),
  ('pypinyin.style._tone_convert',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_tone_convert.py',
   'PYMODULE'),
  ('pypinyin.style._tone_rule',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_tone_rule.py',
   'PYMODULE'),
  ('pypinyin.style._utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\_utils.py',
   'PYMODULE'),
  ('pypinyin.style.bopomofo',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\bopomofo.py',
   'PYMODULE'),
  ('pypinyin.style.braille_mainland',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\braille_mainland.py',
   'PYMODULE'),
  ('pypinyin.style.cyrillic',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\cyrillic.py',
   'PYMODULE'),
  ('pypinyin.style.finals',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\finals.py',
   'PYMODULE'),
  ('pypinyin.style.gwoyeu',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\gwoyeu.py',
   'PYMODULE'),
  ('pypinyin.style.initials',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\initials.py',
   'PYMODULE'),
  ('pypinyin.style.others',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\others.py',
   'PYMODULE'),
  ('pypinyin.style.tone',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\tone.py',
   'PYMODULE'),
  ('pypinyin.style.wadegiles',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\style\\wadegiles.py',
   'PYMODULE'),
  ('pypinyin.utils',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\site-packages\\pypinyin\\utils.py',
   'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('statistics', 'C:\\Users\\<USER>\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('statistics_manager',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\statistics_manager.py',
   'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Users\\<USER>\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Users\\<USER>\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\anaconda3\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\anaconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('txt_processor',
   'C:\\Users\\<USER>\\Documents\\pinyin2\\txt_processor.py',
   'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\anaconda3\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
